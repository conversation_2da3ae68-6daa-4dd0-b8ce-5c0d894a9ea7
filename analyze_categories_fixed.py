﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV分类分析工具
分析output文件夹中的CSV文件，统计顶级分类和子分类信息
只处理超过1万行的CSV文件
"""

import csv
import os
from collections import defaultdict, Counter

def extract_categories(category_string):
    """提取分类层级"""
    if not category_string or category_string.strip() == '':
        return []
    
    # 处理不同的分隔符
    if ' > ' in category_string:
        categories = [cat.strip() for cat in category_string.split(' > ')]
    elif ',' in category_string:
        # 如果有逗号，取第一个分类的层级
        first_category = category_string.split(',')[0].strip()
        if ' > ' in first_category:
            categories = [cat.strip() for cat in first_category.split(' > ')]
        else:
            categories = [first_category]
    else:
        categories = [category_string.strip()]
    
    return categories

def analyze_csv_file(file_path, min_rows=10000):
    """分析单个CSV文件"""
    print(f"分析文件: {os.path.basename(file_path)}")
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)  # 跳过表头
            
            # 查找Categories列
            categories_index = None
            for i, header in enumerate(headers):
                if header.lower() in ['categories', 'category', '产品分类']:
                    categories_index = i
                    break
            
            if categories_index is None:
                print(f"  未找到Categories列")
                return None
            
            # 统计分类信息
            primary_categories = Counter()  # 一级分类统计
            sub_categories = defaultdict(Counter)  # 二级分类统计
            
            row_count = 0
            for row in reader:
                if len(row) <= categories_index:
                    continue
                
                row_count += 1
                category_string = row[categories_index]
                categories = extract_categories(category_string)
                
                if categories:
                    # 一级分类
                    primary_cat = categories[0]
                    primary_categories[primary_cat] += 1
                    
                    # 二级分类
                    if len(categories) > 1:
                        sub_cat = categories[1]
                        sub_categories[primary_cat][sub_cat] += 1
            
            # 检查行数是否满足要求
            if row_count < min_rows:
                print(f"  行数不足 {min_rows} 行，跳过 (实际: {row_count} 行)")
                return None
            
            print(f"  总行数: {row_count}")
            print(f"  一级分类数量: {len(primary_categories)}")
            
            # 获取最多的一级分类
            top_primary = primary_categories.most_common(1)[0] if primary_categories else None
            
            # 获取最多的二级分类（最多5个）
            top_sub_categories = []
            if top_primary and top_primary[0] in sub_categories:
                top_sub_categories = sub_categories[top_primary[0]].most_common(5)
            
            return {
                'filename': os.path.basename(file_path),
                'total_rows': row_count,
                'primary_category': top_primary[0] if top_primary else 'Unknown',
                'primary_count': top_primary[1] if top_primary else 0,
                'sub_categories': top_sub_categories
            }
            
    except Exception as e:
        print(f"  处理文件时出错: {e}")
        return None

def main():
    """主函数"""
    print("CSV分类分析工具")
    print("分析output文件夹中超过1万行的CSV文件")
    print("=" * 60)
    
    output_dir = "output"
    if not os.path.exists(output_dir):
        print(f"错误：{output_dir} 文件夹不存在！")
        return
    
    # 获取所有CSV文件
    csv_files = []
    for file in os.listdir(output_dir):
        if file.lower().endswith('.csv'):
            csv_files.append(os.path.join(output_dir, file))
    
    if not csv_files:
        print("未找到CSV文件！")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    print()
    
    # 分析每个文件
    results = []
    for file_path in csv_files:
        result = analyze_csv_file(file_path, min_rows=10000)
        if result:
            results.append(result)
        print()
    
    # 输出结果
    print("=" * 60)
    print("分析结果:")
    print("=" * 60)
    
    # 表头
    print(f"{'文件名':<30} {'一级分类':<20} {'二级分类1':<15} {'二级分类2':<15} {'二级分类3':<15} {'二级分类4':<15} {'二级分类5':<15}")
    print("-" * 140)
    
    # 数据行
    for result in results:
        filename = result['filename'][:29]  # 限制文件名长度
        primary_cat = result['primary_category'][:19]  # 限制分类名长度
        
        # 构建二级分类列表
        sub_cats = []
        for sub_cat, count in result['sub_categories']:
            sub_cats.append(f"{sub_cat[:14]}({count})")  # 限制长度并显示数量
        
        # 补齐到5个
        while len(sub_cats) < 5:
            sub_cats.append("")
        
        print(f"{filename:<30} {primary_cat:<20} {sub_cats[0]:<15} {sub_cats[1]:<15} {sub_cats[2]:<15} {sub_cats[3]:<15} {sub_cats[4]:<15}")
    
    print()
    print(f"共分析了 {len(results)} 个符合条件的文件")
    
    # 保存结果到CSV文件
    output_file = "category_analysis_result.csv"
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # 写入表头
        writer.writerow(['文件名', '一级分类', '一级分类数量', '二级分类1', '二级分类1数量', 
                        '二级分类2', '二级分类2数量', '二级分类3', '二级分类3数量',
                        '二级分类4', '二级分类4数量', '二级分类5', '二级分类5数量'])
        
        # 写入数据
        for result in results:
            row = [result['filename'], result['primary_category'], result['primary_count']]
            
            for sub_cat, count in result['sub_categories']:
                row.extend([sub_cat, count])
            
            # 补齐空列
            while len(row) < 13:
                row.extend(['', ''])
            
            writer.writerow(row)
    
    print(f"详细结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
