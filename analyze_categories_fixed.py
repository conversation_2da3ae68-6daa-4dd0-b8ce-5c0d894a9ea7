#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版分类分析脚本 - 专门用于分析和修复产品分类问题
"""

import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter, defaultdict
import re

class CategoryAnalyzerFixed:
    def __init__(self):
        # 标准化的分类映射
        self.category_mappings = {
            # 家具类
            'furniture': {
                'keywords': ['chair', 'table', 'desk', 'bed', 'sofa', 'cabinet', 'shelf', 'furniture', 'dresser', 'nightstand'],
                'standard_category': 'Home & Garden > Furniture'
            },
            # 工具类
            'tools': {
                'keywords': ['drill', 'hammer', 'saw', 'wrench', 'tool', 'equipment', 'hardware', 'workshop'],
                'standard_category': 'Tools & Hardware'
            },
            # 电子产品
            'electronics': {
                'keywords': ['tv', 'phone', 'computer', 'tablet', 'electronic', 'device', 'monitor', 'speaker'],
                'standard_category': 'Electronics'
            },
            # 服装类
            'clothing': {
                'keywords': ['shirt', 'pants', 'dress', 'jacket', 'clothing', 'apparel', 'fashion', 'wear'],
                'standard_category': 'Clothing & Accessories'
            },
            # 家居用品
            'home': {
                'keywords': ['kitchen', 'bathroom', 'living', 'home', 'house', 'decor', 'decoration'],
                'standard_category': 'Home & Garden'
            },
            # 运动用品
            'sports': {
                'keywords': ['sport', 'fitness', 'gym', 'exercise', 'outdoor', 'camping', 'hiking'],
                'standard_category': 'Sports & Outdoors'
            },
            # 汽车用品
            'automotive': {
                'keywords': ['car', 'auto', 'vehicle', 'motor', 'automotive', 'parts', 'tire', 'engine'],
                'standard_category': 'Automotive'
            },
            # 宠物用品
            'pets': {
                'keywords': ['pet', 'dog', 'cat', 'animal', 'pet supplies', 'collar', 'leash'],
                'standard_category': 'Pet Supplies'
            },
            # 美容健康
            'beauty': {
                'keywords': ['beauty', 'cosmetic', 'skincare', 'health', 'wellness', 'makeup', 'care'],
                'standard_category': 'Beauty & Personal Care'
            },
            # 玩具游戏
            'toys': {
                'keywords': ['toy', 'game', 'play', 'kids', 'children', 'baby', 'educational'],
                'standard_category': 'Toys & Games'
            }
        }
        
        # 常见分类问题
        self.common_issues = {
            'empty_categories': [],
            'inconsistent_naming': [],
            'too_specific': [],
            'uncategorized': []
        }

    def analyze_existing_categories(self, df: pd.DataFrame, category_column: str):
        """分析现有分类的问题"""
        print(f"🔍 分析分类列: {category_column}")
        
        categories = df[category_column].fillna('').astype(str)
        non_empty = categories[categories != ''].tolist()
        
        print(f"📊 分类统计:")
        print(f"  总产品数: {len(df)}")
        print(f"  有分类的产品: {len(non_empty)} ({len(non_empty)/len(df)*100:.1f}%)")
        print(f"  空分类的产品: {len(df) - len(non_empty)} ({(len(df) - len(non_empty))/len(df)*100:.1f}%)")
        
        # 统计分类分布
        category_counts = Counter(non_empty)
        print(f"  唯一分类数: {len(category_counts)}")
        
        print(f"\n📋 分类分布 (前15个):")
        for i, (cat, count) in enumerate(category_counts.most_common(15), 1):
            percentage = count / len(df) * 100
            print(f"  {i:2d}. {cat:<40} | {count:>4} 个产品 ({percentage:>5.1f}%)")
        
        # 识别问题
        self.identify_category_issues(category_counts, len(df))
        
        return category_counts

    def identify_category_issues(self, category_counts: Counter, total_products: int):
        """识别分类问题"""
        print(f"\n⚠️  分类问题识别:")
        
        issues_found = 0
        
        # 1. 空分类或无意义分类
        meaningless = ['', 'null', 'none', 'n/a', 'undefined', 'uncategorized']
        empty_count = sum(category_counts.get(cat, 0) for cat in meaningless)
        if empty_count > 0:
            print(f"  🔸 空分类/无意义分类: {empty_count} 个产品")
            issues_found += 1
        
        # 2. 分类过于分散
        singleton_categories = sum(1 for count in category_counts.values() if count == 1)
        if singleton_categories > len(category_counts) * 0.3:
            print(f"  🔸 单一产品分类过多: {singleton_categories} 个分类只有1个产品")
            issues_found += 1
        
        # 3. 分类命名不一致
        similar_categories = self.find_similar_categories(list(category_counts.keys()))
        if similar_categories:
            print(f"  🔸 相似分类命名: 发现 {len(similar_categories)} 组相似分类")
            for group in similar_categories[:3]:  # 只显示前3组
                print(f"    - {', '.join(group)}")
            issues_found += 1
        
        # 4. 分类层级不一致
        hierarchical = [cat for cat in category_counts.keys() if '>' in cat or '/' in cat or '|' in cat]
        flat = [cat for cat in category_counts.keys() if not ('>' in cat or '/' in cat or '|' in cat)]
        
        if len(hierarchical) > 0 and len(flat) > 0:
            print(f"  🔸 分类层级不一致: {len(hierarchical)} 个层级分类, {len(flat)} 个平级分类")
            issues_found += 1
        
        if issues_found == 0:
            print(f"  ✅ 未发现明显的分类问题")
        
        return issues_found > 0

    def find_similar_categories(self, categories: list):
        """查找相似的分类名称"""
        similar_groups = []
        processed = set()
        
        for i, cat1 in enumerate(categories):
            if cat1 in processed:
                continue
            
            similar = [cat1]
            
            for j, cat2 in enumerate(categories[i+1:], i+1):
                if cat2 in processed:
                    continue
                
                # 检查相似性
                if self.are_categories_similar(cat1, cat2):
                    similar.append(cat2)
                    processed.add(cat2)
            
            if len(similar) > 1:
                similar_groups.append(similar)
                processed.add(cat1)
        
        return similar_groups

    def are_categories_similar(self, cat1: str, cat2: str):
        """判断两个分类是否相似"""
        cat1_clean = re.sub(r'[^\w\s]', ' ', cat1.lower()).strip()
        cat2_clean = re.sub(r'[^\w\s]', ' ', cat2.lower()).strip()
        
        words1 = set(cat1_clean.split())
        words2 = set(cat2_clean.split())
        
        # 如果有共同词汇且长度相似
        common_words = words1.intersection(words2)
        if len(common_words) > 0 and abs(len(words1) - len(words2)) <= 1:
            return True
        
        # 检查编辑距离
        if self.levenshtein_distance(cat1_clean, cat2_clean) <= 3:
            return True
        
        return False

    def levenshtein_distance(self, s1: str, s2: str):
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self.levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]

    def suggest_category_fixes(self, df: pd.DataFrame, category_column: str, name_column: str = None):
        """建议分类修复方案"""
        print(f"\n🔧 分类修复建议:")
        
        categories = df[category_column].fillna('').astype(str)
        suggestions = []
        
        for i, row in df.iterrows():
            current_category = str(row[category_column]) if pd.notna(row[category_column]) else ''
            
            # 如果分类为空或无意义，尝试从产品名称推断
            if not current_category or current_category.lower() in ['', 'null', 'none', 'n/a', 'undefined']:
                if name_column and name_column in df.columns:
                    product_name = str(row[name_column]) if pd.notna(row[name_column]) else ''
                    suggested_category = self.infer_category_from_name(product_name)
                    
                    if suggested_category != 'Uncategorized':
                        suggestions.append({
                            'index': i,
                            'current': current_category,
                            'suggested': suggested_category,
                            'reason': 'Empty category - inferred from product name',
                            'product_name': product_name
                        })
            
            # 如果分类存在但可能需要标准化
            else:
                standardized = self.standardize_category(current_category)
                if standardized != current_category:
                    suggestions.append({
                        'index': i,
                        'current': current_category,
                        'suggested': standardized,
                        'reason': 'Category standardization',
                        'product_name': str(row[name_column]) if name_column and name_column in df.columns else ''
                    })
        
        print(f"📋 修复建议数量: {len(suggestions)}")
        
        if suggestions:
            print(f"\n前10个修复建议:")
            for i, suggestion in enumerate(suggestions[:10], 1):
                print(f"  {i:2d}. 产品: {suggestion['product_name'][:30]}")
                print(f"      当前分类: {suggestion['current']}")
                print(f"      建议分类: {suggestion['suggested']}")
                print(f"      原因: {suggestion['reason']}")
                print()
        
        return suggestions

    def infer_category_from_name(self, product_name: str):
        """从产品名称推断分类"""
        if not product_name:
            return 'Uncategorized'
        
        name_lower = product_name.lower()
        
        for category_type, info in self.category_mappings.items():
            keywords = info['keywords']
            if any(keyword in name_lower for keyword in keywords):
                return info['standard_category']
        
        return 'Uncategorized'

    def standardize_category(self, category: str):
        """标准化分类名称"""
        if not category:
            return 'Uncategorized'
        
        category_lower = category.lower()
        
        # 直接匹配标准分类
        for category_type, info in self.category_mappings.items():
            standard_cat = info['standard_category']
            if category_lower == standard_cat.lower():
                return standard_cat
            
            # 检查是否包含关键词
            keywords = info['keywords']
            if any(keyword in category_lower for keyword in keywords):
                return standard_cat
        
        # 如果没有匹配，保持原样但清理格式
        cleaned = ' '.join(word.capitalize() for word in category.split())
        return cleaned

    def apply_category_fixes(self, df: pd.DataFrame, suggestions: list, category_column: str):
        """应用分类修复"""
        print(f"🔧 应用分类修复...")
        
        df_fixed = df.copy()
        applied_count = 0
        
        for suggestion in suggestions:
            index = suggestion['index']
            new_category = suggestion['suggested']
            
            df_fixed.at[index, category_column] = new_category
            applied_count += 1
        
        print(f"✅ 已应用 {applied_count} 个修复建议")
        
        return df_fixed

    def analyze_and_fix_file(self, input_file: str, output_file: str = None):
        """分析并修复文件中的分类问题"""
        try:
            print(f"📁 分析文件: {Path(input_file).name}")
            
            # 读取数据
            if input_file.endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)
            
            print(f"📊 数据概况: {len(df)} 行, {len(df.columns)} 列")
            
            # 查找分类列
            category_columns = []
            for col in df.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['category', 'categories', 'type', 'class']):
                    category_columns.append(col)
            
            if not category_columns:
                print("❌ 未找到分类列")
                return None
            
            category_col = category_columns[0]
            print(f"🎯 使用分类列: {category_col}")
            
            # 查找产品名称列
            name_col = None
            for col in df.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['name', 'title', 'product']):
                    name_col = col
                    break
            
            print(f"📝 产品名称列: {name_col}")
            
            # 分析现有分类
            category_counts = self.analyze_existing_categories(df, category_col)
            
            # 生成修复建议
            suggestions = self.suggest_category_fixes(df, category_col, name_col)
            
            # 应用修复
            if suggestions:
                df_fixed = self.apply_category_fixes(df, suggestions, category_col)
                
                # 保存修复后的文件
                if output_file is None:
                    input_path = Path(input_file)
                    output_file = f"{input_path.stem}_categories_fixed.csv"
                
                df_fixed.to_csv(output_file, index=False, encoding='utf-8-sig')
                
                print(f"📁 修复后文件: {output_file}")
                
                # 重新分析修复后的分类
                print(f"\n📊 修复后分类分析:")
                self.analyze_existing_categories(df_fixed, category_col)
                
                return output_file
            else:
                print("✅ 未发现需要修复的分类问题")
                return input_file
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    analyzer = CategoryAnalyzerFixed()
    
    print("🔧 分类分析修复工具")
    print("="*50)
    
    print("📋 使用方法:")
    print("analyzer.analyze_and_fix_file('input.csv', 'output.csv')")

if __name__ == "__main__":
    main()
