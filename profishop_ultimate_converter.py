#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Profishop-DE 终极优化版转换器 - 解决所有细节问题
"""

import pandas as pd
import re
import logging
import random
from pathlib import Path
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProfishopUltimateConverter:
    def __init__(self):
        self.sku_counter = 1
        self.sku_prefix = "DE-PROF"
        self.used_skus = set()
        
        # 德语CTA关键词模式
        self.cta_patterns = [
            r'\s*billig\s*versandkostenfrei\s*online\s*kaufen\s*',
            r'\s*:\s*riesige\s*Auswahl\s*billige\s*Verkaufspreise\s*',
            r'\s*billig\s*versandkostenfrei\s*',
            r'\s*riesige\s*Auswahl\s*',
            r'\s*billige\s*Verkaufspreise\s*',
            r'\s*online\s*kaufen\s*',
            r'\s*günstig\s*bestellen\s*',
            r'\s*schneller\s*versand\s*',
            r'\s*kostenloser\s*versand\s*'
        ]
        
        # 德语停用词 (用于标签优化)
        self.german_stopwords = {
            'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'einen', 'einem',
            'einer', 'eines', 'und', 'oder', 'aber', 'mit', 'von', 'zu', 'auf', 'in',
            'an', 'bei', 'nach', 'vor', 'über', 'unter', 'durch', 'für', 'ohne',
            'gegen', 'um', 'bis', 'seit', 'während', 'wegen', 'trotz', 'statt'
        }
        
        # 品牌标准化映射
        self.brand_standardization = {
            'bosch': 'Bosch',
            'makita': 'Makita',
            'dewalt': 'DeWalt',
            'milwaukee': 'Milwaukee',
            'festool': 'Festool',
            'hilti': 'Hilti',
            'metabo': 'Metabo',
            'ryobi': 'Ryobi',
            'black+decker': 'Black+Decker',
            'craftsman': 'Craftsman'
        }
        
        # 字段映射
        self.field_mapping = {
            'id': 'ID',
            'name': 'title',
            'description': 'alldes',
            'short_description': 'description s',
            'basic_description': 'description',
            'price': 'price',
            'sku_original': 'sku',
            'brand': 'Brand',
            'manufacturer': 'MFG',
            'category': 'category',
            'image_primary': 'image',
            'image_secondary': 'S-IMANGE',
            'tags': 'tags',
            'upc': 'UPC',
            'page_url': 'PageUrl'
        }

    def clean_title_cta(self, title: str) -> str:
        """清理标题中的CTA文本"""
        if not title or str(title).strip() == 'nan':
            return ""
        
        cleaned_title = str(title)
        
        # 移除CTA模式
        for pattern in self.cta_patterns:
            cleaned_title = re.sub(pattern, '', cleaned_title, flags=re.IGNORECASE)
        
        # 清理结尾的冒号和多余空格
        cleaned_title = re.sub(r'\s*:\s*$', '', cleaned_title)
        cleaned_title = re.sub(r'\s+', ' ', cleaned_title).strip()
        
        return cleaned_title

    def smart_image_processing(self, image_str: str) -> str:
        """智能图片分隔符处理"""
        if not image_str or str(image_str).strip() == 'nan':
            return ""
        
        image_text = str(image_str).strip()
        
        # 智能分隔符检测 (按优先级)
        separators = ['|||', '||', '|', ';', ' ']
        detected_separator = None
        
        for sep in separators:
            if sep in image_text and len(image_text.split(sep)) > 1:
                detected_separator = sep
                break
        
        if detected_separator:
            # 分割并清理图片URL
            images = []
            for img in image_text.split(detected_separator):
                img = img.strip()
                if img and ('http' in img or img.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp'))):
                    images.append(img)
            
            # 用逗号连接
            return ', '.join(images) if images else image_text
        
        return image_text

    def fix_sku_dots(self, sku: str) -> str:
        """修复SKU中的点号"""
        if not sku:
            return sku
        
        # 将点号替换为横杠
        fixed_sku = str(sku).replace('.', '-')
        
        # 避免连续横杠
        fixed_sku = re.sub(r'-+', '-', fixed_sku)
        
        return fixed_sku

    def optimize_description_h3(self, description: str) -> str:
        """优化描述中的H3标签问题"""
        if not description or str(description).strip() == 'nan':
            return ""
        
        content = str(description)
        
        # 移除开头的 <h3>Beschreibung</h3> 及其后的换行
        content = re.sub(r'^<h3>Beschreibung</h3>\s*', '', content, flags=re.IGNORECASE)
        content = re.sub(r'^<h3>Product\s+Description</h3>\s*', '', content, flags=re.IGNORECASE)
        content = re.sub(r'^<h3>Description</h3>\s*', '', content, flags=re.IGNORECASE)
        
        # 清理其他可能的描述标题
        content = re.sub(r'^<h[1-6][^>]*>Beschreibung</h[1-6]>\s*', '', content, flags=re.IGNORECASE)
        
        return content.strip()

    def optimize_tags(self, tags_str: str) -> str:
        """智能标签优化"""
        if not tags_str or str(tags_str).strip() == 'nan':
            return ""
        
        tags_text = str(tags_str)
        
        # 智能分隔符检测
        if ',' in tags_text:
            tags = [tag.strip() for tag in tags_text.split(',')]
        elif ';' in tags_text:
            tags = [tag.strip() for tag in tags_text.split(';')]
        elif '|' in tags_text:
            tags = [tag.strip() for tag in tags_text.split('|')]
        else:
            # 按空格分割，但保留多词标签
            tags = [tag.strip() for tag in re.split(r'\s{2,}', tags_text)]
        
        # 清理和优化标签
        optimized_tags = []
        for tag in tags:
            if not tag:
                continue
                
            tag_clean = tag.strip().lower()
            
            # 过滤停用词和短词
            if len(tag_clean) > 2 and tag_clean not in self.german_stopwords:
                # 首字母大写
                tag_formatted = tag_clean.capitalize()
                
                # 避免重复
                if tag_formatted not in optimized_tags:
                    optimized_tags.append(tag_formatted)
        
        # 限制标签数量 (最多8个)
        final_tags = optimized_tags[:8]
        
        return ', '.join(final_tags)

    def enhanced_description_extraction(self, row: pd.Series) -> tuple:
        """增强版描述提取 - 解决profishop-de-6问题"""
        
        # 尝试多个描述字段
        description_sources = [
            self.get_field_value(row, 'description'),      # alldes
            self.get_field_value(row, 'basic_description'), # description
            self.get_field_value(row, 'short_description')  # description s
        ]
        
        # 找到最佳描述源
        best_description = ""
        for desc in description_sources:
            if desc and len(desc.strip()) > len(best_description):
                best_description = desc.strip()
        
        if not best_description:
            # 如果没有描述，基于产品名称生成
            name = self.get_field_value(row, 'name')
            brand = self.get_field_value(row, 'brand')
            if name:
                best_description = f"<p>High-quality {name}"
                if brand:
                    best_description += f" from {brand}"
                best_description += ". Professional grade product with excellent performance.</p>"
        
        # 优化描述
        full_desc = self.optimize_description_h3(best_description)
        full_desc = self.clean_html_smart(full_desc)
        
        # 生成简短描述
        try:
            soup = BeautifulSoup(full_desc, 'html.parser')
            text = soup.get_text()
            short_desc = text[:150] + "..." if len(text) > 150 else text
        except:
            # 备用方案
            text_only = re.sub(r'<[^>]+>', '', full_desc)
            short_desc = text_only[:150] + "..." if len(text_only) > 150 else text_only
        
        return full_desc, short_desc

    def clean_html_smart(self, html_content: str) -> str:
        """智能HTML清理"""
        if not html_content or str(html_content).strip() == 'nan':
            return ""
        
        content = str(html_content)
        
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除危险标签
            for tag in soup(['script', 'style', 'iframe', 'object', 'embed']):
                tag.decompose()
            
            # 智能标签替换
            for h1 in soup.find_all(['h1', 'h2']):
                h1.name = 'h3'
            
            for font in soup.find_all('font'):
                font.name = 'span'
            
            for center in soup.find_all('center'):
                center.name = 'div'
                center['style'] = 'text-align: center;'
            
            # 清理空标签
            for tag in soup.find_all():
                if not tag.get_text(strip=True) and not tag.find_all():
                    tag.decompose()
            
            # 移除事件属性
            for tag in soup.find_all():
                attrs_to_remove = [attr for attr in tag.attrs if attr.startswith('on')]
                for attr in attrs_to_remove:
                    del tag[attr]
            
            return str(soup)
            
        except:
            # 备用正则表达式清理
            replacements = {
                r'<h[12]([^>]*)>': r'<h3\1>',
                r'</h[12]>': '</h3>',
                r'<font([^>]*)>': r'<span\1>',
                r'</font>': '</span>',
                r'<center([^>]*)>': r'<div style="text-align: center;"\1>',
                r'</center>': '</div>'
            }
            
            for pattern, replacement in replacements.items():
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            
            return content.strip()

    def generate_ultimate_sku(self, row: pd.Series) -> str:
        """终极SKU生成 - 修复点号问题"""
        
        # 策略1: 使用原始SKU (修复点号)
        original_sku = self.get_field_value(row, 'sku_original')
        if original_sku and len(original_sku) <= 15:
            fixed_sku = self.fix_sku_dots(original_sku)
            sku = f"{self.sku_prefix}-{fixed_sku}"
            if sku not in self.used_skus:
                self.used_skus.add(sku)
                return sku
        
        # 策略2: 使用ID
        product_id = self.get_field_value(row, 'id')
        if product_id:
            sku = f"{self.sku_prefix}-{product_id}"
            if sku not in self.used_skus:
                self.used_skus.add(sku)
                return sku
        
        # 策略3: 智能生成
        brand = self.standardize_brand(self.get_field_value(row, 'brand'))
        name = self.get_field_value(row, 'name')
        
        components = [self.sku_prefix]
        
        if brand:
            brand_abbr = re.sub(r'[^\w]', '', brand)[:4].upper()
            components.append(brand_abbr)
        
        if name:
            name_words = re.findall(r'\b\w{3,}\b', name.lower())
            if name_words:
                feature_part = ''.join([word[:3].upper() for word in name_words[:2]])
                components.append(feature_part[:6])
        
        components.append(f"{self.sku_counter:04d}")
        
        sku = '-'.join(components)
        
        # 确保唯一性和长度
        while sku in self.used_skus or len(sku) > 25:
            self.sku_counter += 1
            components[-1] = f"{self.sku_counter:04d}"
            sku = '-'.join(components)
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku

    def standardize_brand(self, brand: str) -> str:
        """标准化品牌名称"""
        if not brand or str(brand).strip() == 'nan':
            return ''
        
        brand_clean = str(brand).strip()
        brand_lower = brand_clean.lower()
        
        # 查找标准化映射
        for key, standard in self.brand_standardization.items():
            if key in brand_lower:
                return standard
        
        # 首字母大写
        return brand_clean.title()

    def get_field_value(self, row: pd.Series, field_key: str) -> str:
        """获取字段值"""
        field_name = self.field_mapping.get(field_key)
        if field_name and field_name in row.index and pd.notna(row[field_name]):
            return str(row[field_name]).strip()
        return ''

    def calculate_brand_aware_price(self, regular_price: float, brand: str = "") -> float:
        """基于品牌的智能定价"""
        if regular_price <= 0:
            return 0.0
        
        # 小于10的不打折
        if regular_price < 10:
            return regular_price
        
        # 基础折扣率
        if regular_price <= 50:
            base_discount = random.uniform(0.4, 0.7)
        elif regular_price <= 200:
            base_discount = random.uniform(0.6, 0.8)
        elif regular_price <= 500:
            base_discount = random.uniform(0.75, 0.9)
        else:
            base_discount = random.uniform(0.85, 0.95)
        
        # 品牌调整
        brand_lower = str(brand).lower() if brand else ""
        
        # 高端品牌折扣较少
        premium_brands = ['bosch', 'makita', 'dewalt', 'milwaukee', 'festool', 'hilti']
        if any(premium in brand_lower for premium in premium_brands):
            base_discount = min(base_discount + 0.1, 0.95)
        
        # 普通品牌可以更多折扣
        budget_brands = ['ryobi', 'black+decker', 'craftsman']
        if any(budget in brand_lower for budget in budget_brands):
            base_discount = max(base_discount - 0.1, 0.3)
        
        sale_price = regular_price * base_discount
        return round(sale_price, 2)

    def process_images_ultimate(self, row: pd.Series) -> str:
        """终极图片处理 - 智能分隔符 + 替补逻辑"""

        # 优先使用主图片
        primary_image = self.get_field_value(row, 'image_primary')

        if primary_image and primary_image.strip():
            return self.smart_image_processing(primary_image)

        # 主图片缺失时使用备用图片
        secondary_image = self.get_field_value(row, 'image_secondary')

        if secondary_image and secondary_image.strip():
            return self.smart_image_processing(secondary_image)

        return ""

    def convert_row_ultimate(self, row: pd.Series) -> Dict:
        """终极版行转换 - 解决所有细节问题"""
        wc_row = {}

        # 基础信息
        wc_row['ID'] = ''
        wc_row['Type'] = 'simple'
        wc_row['SKU'] = self.generate_ultimate_sku(row)

        # 产品名称 - 清理CTA
        raw_name = self.get_field_value(row, 'name')
        wc_row['Name'] = self.clean_title_cta(raw_name)

        # 增强版描述处理
        full_desc, short_desc = self.enhanced_description_extraction(row)
        wc_row['Description'] = full_desc
        wc_row['Short description'] = short_desc

        # 品牌标准化
        brand = self.standardize_brand(self.get_field_value(row, 'brand'))

        # 智能定价
        price_str = self.get_field_value(row, 'price')
        try:
            regular_price = float(price_str) if price_str else 0.0
        except:
            regular_price = 0.0

        sale_price = self.calculate_brand_aware_price(regular_price, brand)

        wc_row['Regular price'] = regular_price
        wc_row['Sale price'] = sale_price

        # 分类处理
        category = self.get_field_value(row, 'category')
        wc_row['Categories'] = category if category else "Tools"

        # 标签优化
        raw_tags = self.get_field_value(row, 'tags')
        wc_row['Tags'] = self.optimize_tags(raw_tags)

        # 终极图片处理
        wc_row['Images'] = self.process_images_ultimate(row)

        # 外部链接
        page_url = self.get_field_value(row, 'page_url')
        wc_row['External URL'] = page_url
        wc_row['Button text'] = 'View on Profishop' if page_url else ''

        # 属性
        manufacturer = self.get_field_value(row, 'manufacturer')
        upc = self.get_field_value(row, 'upc')

        wc_row['Attribute 1 name'] = 'Brand'
        wc_row['Attribute 1 value(s)'] = brand
        wc_row['Attribute 1 visible'] = 1
        wc_row['Attribute 1 global'] = 0

        wc_row['Attribute 2 name'] = 'Manufacturer'
        wc_row['Attribute 2 value(s)'] = manufacturer
        wc_row['Attribute 2 visible'] = 1
        wc_row['Attribute 2 global'] = 0

        wc_row['Attribute 3 name'] = 'UPC'
        wc_row['Attribute 3 value(s)'] = upc
        wc_row['Attribute 3 visible'] = 0
        wc_row['Attribute 3 global'] = 0

        # 优化的库存设置 - 不跟踪库存
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        wc_row['In stock?'] = 1
        wc_row['Stock'] = ''  # 空值，不跟踪具体数量
        wc_row['Low stock amount'] = ''  # 空值
        wc_row['Backorders allowed?'] = 0  # 不允许缺货订购
        wc_row['Sold individually?'] = 0  # 可批量购买
        wc_row['Allow customer reviews?'] = 1
        wc_row['Purchase note'] = ''
        wc_row['Date sale price starts'] = ''
        wc_row['Date sale price ends'] = ''
        wc_row['Shipping class'] = ''

        return wc_row

    def process_profishop_ultimate(self, input_file: Path) -> bool:
        """终极版Profishop文件处理"""
        try:
            logger.info(f"🚀 开始终极处理: {input_file.name}")

            # 读取数据
            df = pd.read_excel(input_file)
            logger.info(f"📊 读取数据: {len(df)} 行, {len(df.columns)} 列")

            # 数据预处理统计
            stats = {
                'total_products': len(df),
                'cta_cleaned': 0,
                'images_processed': 0,
                'skus_fixed': 0,
                'descriptions_enhanced': 0,
                'tags_optimized': 0,
                'h3_fixed': 0
            }

            # 转换处理
            wc_rows = []

            for index, row in df.iterrows():
                if index % 1000 == 0 and index > 0:
                    logger.info(f"⏳ 已处理 {index}/{len(df)} 行...")

                wc_row = self.convert_row_ultimate(row)
                wc_rows.append(wc_row)

                # 统计优化功能使用情况
                original_name = self.get_field_value(row, 'name')
                if original_name != wc_row['Name']:
                    stats['cta_cleaned'] += 1

                if wc_row['Images']:
                    stats['images_processed'] += 1

                if '.' in str(self.get_field_value(row, 'sku_original')):
                    stats['skus_fixed'] += 1

                if wc_row['Description']:
                    stats['descriptions_enhanced'] += 1

                if wc_row['Tags']:
                    stats['tags_optimized'] += 1

                original_desc = self.get_field_value(row, 'description')
                if '<h3>Beschreibung</h3>' in str(original_desc):
                    stats['h3_fixed'] += 1

            # 创建DataFrame并去重
            wc_df = pd.DataFrame(wc_rows)

            # 去重处理
            original_count = len(wc_df)
            wc_df = wc_df.drop_duplicates(subset=['SKU'], keep='first')
            duplicates_removed = original_count - len(wc_df)

            if duplicates_removed > 0:
                logger.info(f"🔄 去重: 移除 {duplicates_removed} 个重复SKU")

            # 保存结果
            output_dir = Path("woocommerce_output_profishop_ultimate")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / f"{input_file.stem}_ultimate.csv"

            # 确保所有WooCommerce列都存在
            wc_columns = [
                'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
                'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
                'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
                'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
                'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
                'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
                'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
                'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
                'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
                'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global'
            ]

            # 添加缺失的列
            for col in wc_columns:
                if col not in wc_df.columns:
                    wc_df[col] = ''

            # 按标准列顺序输出
            wc_df = wc_df[wc_columns]

            # 保存CSV
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            # 详细报告
            logger.info(f"✅ 终极转换完成: {output_file}")
            logger.info(f"📊 优化统计:")
            logger.info(f"  • 总产品数: {stats['total_products']:,}")
            logger.info(f"  • 最终输出: {len(wc_df):,}")
            logger.info(f"  • CTA清理: {stats['cta_cleaned']}")
            logger.info(f"  • 图片处理: {stats['images_processed']}")
            logger.info(f"  • SKU修复: {stats['skus_fixed']}")
            logger.info(f"  • 描述增强: {stats['descriptions_enhanced']}")
            logger.info(f"  • 标签优化: {stats['tags_optimized']}")
            logger.info(f"  • H3标签修复: {stats['h3_fixed']}")

            return True

        except Exception as e:
            logger.error(f"❌ 处理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

def main():
    """主函数"""
    converter = ProfishopUltimateConverter()

    source_dir = Path("源数据文件/de")

    if not source_dir.exists():
        logger.error("❌ 德国数据目录不存在!")
        return

    profishop_files = [f for f in source_dir.glob("profishop-de*.xlsx") if not f.name.startswith('~')]

    if not profishop_files:
        logger.error("❌ 没有找到profishop-de文件!")
        return

    logger.info(f"🚀 Profishop-DE 终极优化版转换器")
    logger.info(f"📂 数据目录: {source_dir}")
    logger.info(f"📊 找到 {len(profishop_files)} 个文件")

    logger.info(f"✨ 终极优化特性:")
    logger.info(f"  🧹 智能CTA文本清理")
    logger.info(f"  🖼️  智能图片分隔符处理")
    logger.info(f"  🏷️  SKU点号修复")
    logger.info(f"  📝 H3描述标签优化")
    logger.info(f"  🏷️  智能标签优化")
    logger.info(f"  📦 库存设置优化")
    logger.info(f"  🔍 profishop-de-6问题修复")

    success_count = 0
    total_products = 0

    for i, file in enumerate(profishop_files, 1):
        logger.info(f"\n[{i}/{len(profishop_files)}] 处理文件: {file.name}")

        if converter.process_profishop_ultimate(file):
            success_count += 1

            # 统计产品数
            try:
                output_file = Path("woocommerce_output_profishop_ultimate") / f"{file.stem}_ultimate.csv"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8-sig') as f:
                        line_count = sum(1 for _ in f) - 1
                    total_products += line_count
            except:
                pass

    # 最终报告
    logger.info(f"\n" + "="*70)
    logger.info(f"🎉 Profishop-DE 终极优化转换完成!")
    logger.info(f"="*70)
    logger.info(f"✅ 成功处理: {success_count}/{len(profishop_files)} 个文件")
    logger.info(f"📊 总产品数: {total_products:,} 个")
    logger.info(f"📁 输出目录: woocommerce_output_profishop_ultimate/")

    if success_count == len(profishop_files):
        logger.info(f"🎯 所有文件转换成功!")
        logger.info(f"💡 所有细节问题已修复:")
        logger.info(f"  ✅ CTA文本智能清理")
        logger.info(f"  ✅ 图片分隔符统一为逗号")
        logger.info(f"  ✅ SKU点号替换为横杠")
        logger.info(f"  ✅ H3描述标签优化")
        logger.info(f"  ✅ 标签智能优化")
        logger.info(f"  ✅ 库存设置为不跟踪模式")
    else:
        logger.warning(f"⚠️  部分文件处理失败，请检查日志")

if __name__ == "__main__":
    main()
