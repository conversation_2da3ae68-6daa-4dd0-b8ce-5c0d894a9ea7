#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版标签和分类优化逻辑
"""

import re
from typing import List

class FixedTagsCategoriesOptimizer:
    def __init__(self):
        # 扩展的德语停用词
        self.german_stopwords = {
            # 基础停用词
            'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'einen', 'einem',
            'einer', 'eines', 'und', 'oder', 'aber', 'mit', 'von', 'zu', 'auf', 'in',
            'an', 'bei', 'nach', 'vor', 'über', 'unter', 'durch', 'für', 'ohne',
            'gegen', 'um', 'bis', 'seit', 'während', 'wegen', 'trotz', 'statt',
            # 英语停用词
            'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            # 无意义词汇
            'this', 'that', 'these', 'those', 'here', 'there', 'where', 'when', 'how',
            'what', 'who', 'why', 'which', 'can', 'will', 'would', 'could', 'should',
            # 德语无意义词
            'hier', 'dort', 'wo', 'wann', 'wie', 'was', 'wer', 'warum', 'welche',
            'kann', 'wird', 'würde', 'könnte', 'sollte', 'haben', 'sein', 'werden'
        }
        
        # 专业工具分类映射
        self.category_mapping = {
            # 电动工具
            'drill': 'Tools > Power Tools > Drills',
            'bohrer': 'Tools > Power Tools > Drills',
            'saw': 'Tools > Power Tools > Saws',
            'säge': 'Tools > Power Tools > Saws',
            'grinder': 'Tools > Power Tools > Grinders',
            'schleifer': 'Tools > Power Tools > Grinders',
            'sander': 'Tools > Power Tools > Sanders',
            'schleifmaschine': 'Tools > Power Tools > Sanders',
            
            # 手工具
            'hammer': 'Tools > Hand Tools > Hammers',
            'screwdriver': 'Tools > Hand Tools > Screwdrivers',
            'schraubendreher': 'Tools > Hand Tools > Screwdrivers',
            'wrench': 'Tools > Hand Tools > Wrenches',
            'schlüssel': 'Tools > Hand Tools > Wrenches',
            'pliers': 'Tools > Hand Tools > Pliers',
            'zange': 'Tools > Hand Tools > Pliers',
            
            # 测量工具
            'measuring': 'Tools > Measuring Tools',
            'measure': 'Tools > Measuring Tools',
            'ruler': 'Tools > Measuring Tools',
            'lineal': 'Tools > Measuring Tools',
            'level': 'Tools > Measuring Tools',
            'wasserwaage': 'Tools > Measuring Tools',
            
            # 安全设备
            'safety': 'Safety > Personal Protection',
            'sicherheit': 'Safety > Personal Protection',
            'helmet': 'Safety > Personal Protection > Helmets',
            'helm': 'Safety > Personal Protection > Helmets',
            'gloves': 'Safety > Personal Protection > Gloves',
            'handschuhe': 'Safety > Personal Protection > Gloves',
            
            # 照明
            'light': 'Tools > Lighting > Work Lights',
            'licht': 'Tools > Lighting > Work Lights',
            'flashlight': 'Tools > Lighting > Flashlights',
            'taschenlampe': 'Tools > Lighting > Flashlights',
            
            # 存储
            'storage': 'Tools > Storage > Tool Storage',
            'lagerung': 'Tools > Storage > Tool Storage',
            'box': 'Tools > Storage > Tool Boxes',
            'kasten': 'Tools > Storage > Tool Boxes'
        }

    def optimize_tags_fixed(self, tags_str: str) -> str:
        """修复版标签优化"""
        if not tags_str or str(tags_str).strip() == 'nan':
            return ""
        
        tags_text = str(tags_str)
        
        # 智能分隔符检测
        if ',' in tags_text:
            tags = [tag.strip() for tag in tags_text.split(',')]
        elif ';' in tags_text:
            tags = [tag.strip() for tag in tags_text.split(';')]
        elif '|' in tags_text:
            tags = [tag.strip() for tag in tags_text.split('|')]
        else:
            # 按多个空格分割，保留多词标签
            tags = [tag.strip() for tag in re.split(r'\s{2,}', tags_text)]
        
        # 清理和优化标签
        optimized_tags = []
        for tag in tags:
            if not tag:
                continue
            
            tag_clean = tag.strip().lower()
            
            # 修复版过滤条件
            if self.is_valid_tag(tag_clean):
                # 首字母大写，保持多词标签的格式
                tag_formatted = self.format_tag(tag_clean)
                
                # 避免重复
                if tag_formatted not in optimized_tags:
                    optimized_tags.append(tag_formatted)
        
        # 限制标签数量 (最多6个高质量标签)
        final_tags = optimized_tags[:6]
        
        return ', '.join(final_tags)

    def is_valid_tag(self, tag: str) -> bool:
        """检查标签是否有效"""
        
        # 1. 长度检查 (3-20字符)
        if len(tag) < 3 or len(tag) > 20:
            return False
        
        # 2. 纯数字检查 ❌ 修复关键问题
        if tag.isdigit():
            return False
        
        # 3. 停用词检查
        if tag in self.german_stopwords:
            return False
        
        # 4. 特殊字符检查 (只允许字母、数字、空格、连字符)
        if not re.match(r'^[a-zA-Z0-9\s\-_äöüÄÖÜß]+$', tag):
            return False
        
        # 5. 有意义内容检查 (至少包含一个字母)
        if not re.search(r'[a-zA-ZäöüÄÖÜß]', tag):
            return False
        
        # 6. 避免纯符号
        if re.match(r'^[\-_\s]+$', tag):
            return False
        
        return True

    def format_tag(self, tag: str) -> str:
        """格式化标签"""
        # 处理多词标签
        words = tag.split()
        formatted_words = []
        
        for word in words:
            if len(word) > 0:
                # 首字母大写，保持德语特殊字符
                formatted_word = word[0].upper() + word[1:].lower()
                formatted_words.append(formatted_word)
        
        return ' '.join(formatted_words)

    def enhance_category_fixed(self, category: str, tags: str = "", brand: str = "", name: str = "") -> str:
        """修复版分类增强"""
        if not category or str(category).strip() == 'nan':
            category = "Tools"
        
        category = str(category).strip()
        
        # 1. 基于标签增强分类
        if tags and str(tags).strip() != 'nan':
            tags_lower = str(tags).lower()
            
            for keyword, enhanced_cat in self.category_mapping.items():
                if keyword in tags_lower:
                    return enhanced_cat
        
        # 2. 基于产品名称增强分类
        if name and str(name).strip() != 'nan':
            name_lower = str(name).lower()
            
            for keyword, enhanced_cat in self.category_mapping.items():
                if keyword in name_lower:
                    return enhanced_cat
        
        # 3. 基于品牌增强分类
        if brand and str(brand).strip() != 'nan':
            brand_lower = str(brand).lower()
            
            # 知名工具品牌自动归类到工具
            tool_brands = ['bosch', 'makita', 'dewalt', 'milwaukee', 'festool', 'hilti', 'metabo']
            if any(tool_brand in brand_lower for tool_brand in tool_brands):
                if 'tool' not in category.lower():
                    return f"Tools > {category}"
        
        # 4. 处理分类层级
        if '>' not in category and category.lower() != 'tools':
            # 如果是单层分类且不是"Tools"，添加Tools前缀
            return f"Tools > {category}"
        
        return category

    def test_optimization(self):
        """测试优化效果"""
        print("🧪 标签和分类优化测试")
        print("="*50)
        
        # 测试标签优化
        test_tags = [
            "drill, 123, power, 456, tool",  # 包含纯数字
            "LED;light;work;bright;789",     # 分号分隔 + 数字
            "safety helmet protection der die",  # 包含停用词
            "Bosch Professional Werkzeug",   # 正常标签
            "a, bb, 12, good, tool, excellent"  # 混合问题
        ]
        
        print("🏷️ 标签优化测试:")
        for i, tags in enumerate(test_tags, 1):
            optimized = self.optimize_tags_fixed(tags)
            print(f"  {i}. 原始: {tags}")
            print(f"     优化: {optimized}")
            print()
        
        # 测试分类增强
        test_cases = [
            {"category": "Power Tools", "tags": "drill, bosch", "brand": "Bosch", "name": "Professional Drill"},
            {"category": "Tools", "tags": "safety, helmet", "brand": "", "name": "Safety Helmet"},
            {"category": "Equipment", "tags": "light, led, work", "brand": "Philips", "name": "LED Work Light"},
            {"category": "", "tags": "measuring, ruler", "brand": "", "name": "Measuring Ruler"}
        ]
        
        print("📂 分类增强测试:")
        for i, case in enumerate(test_cases, 1):
            enhanced = self.enhance_category_fixed(
                case["category"], 
                case["tags"], 
                case["brand"], 
                case["name"]
            )
            print(f"  {i}. 原始分类: {case['category']}")
            print(f"     标签: {case['tags']}")
            print(f"     增强分类: {enhanced}")
            print()

def main():
    """主函数"""
    optimizer = FixedTagsCategoriesOptimizer()
    optimizer.test_optimization()
    
    print("💡 修复要点总结:")
    print("="*50)
    print("✅ 标签优化修复:")
    print("  • 添加 tag.isdigit() 检查，完全过滤纯数字标签")
    print("  • 扩展停用词列表 (德语+英语)")
    print("  • 增强特殊字符过滤")
    print("  • 改进标签格式化")
    print("  • 限制标签数量为6个")
    
    print("\n✅ 分类增强修复:")
    print("  • 实际调用分类增强函数")
    print("  • 基于标签、产品名称、品牌的多维度增强")
    print("  • 专业工具分类映射")
    print("  • 自动添加Tools层级")

if __name__ == "__main__":
    main()
