#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能HTML清理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_woocommerce_converter import EnhancedWooCommerceConverter

def test_html_cleaning():
    """测试各种HTML清理场景"""
    print("🧪 测试智能HTML清理功能")
    print("="*80)
    
    converter = EnhancedWooCommerceConverter()
    
    test_cases = [
        {
            "name": "基础HTML标签替换",
            "input": '<font color="red" size="3">红色文字</font><center>居中文本</center>',
            "expected_features": ["font→span", "center→div"]
        },
        {
            "name": "标题标签层级优化",
            "input": '<h1>主标题</h1><h2>副标题</h2><h5>小标题</h5>',
            "expected_features": ["h1→h3", "h2→h3", "h5→h4"]
        },
        {
            "name": "危险标签移除",
            "input": '<script>alert("危险")</script><p>安全内容</p><iframe src="bad.html"></iframe>',
            "expected_features": ["移除script", "移除iframe", "保留p"]
        },
        {
            "name": "表格样式优化",
            "input": '<table border="1" style="width:100%"><tr><td>单元格</td></tr></table>',
            "expected_features": ["添加WooCommerce样式", "移除内联样式"]
        },
        {
            "name": "div转p标签",
            "input": '<div>这是一段纯文本内容，应该转换为段落标签</div><div><p>这个div包含p标签，不应该转换</p></div>',
            "expected_features": ["纯文本div→p", "保留复杂div"]
        },
        {
            "name": "属性清理",
            "input": '<p onclick="alert()" style="color:red" class="text">段落</p><a href="http://example.com" onclick="bad()">链接</a>',
            "expected_features": ["移除onclick", "保留href", "移除style"]
        },
        {
            "name": "空标签移除",
            "input": '<p></p><div>   </div><span>有内容</span><p><br></p>',
            "expected_features": ["移除空标签", "保留有内容标签"]
        },
        {
            "name": "HTML实体转换",
            "input": '&nbsp;空格&amp;符号&lt;小于&gt;大于&quot;引号&#39;单引号',
            "expected_features": ["转换HTML实体"]
        },
        {
            "name": "复杂嵌套结构",
            "input": '''
            <div style="background:red">
                <font size="4" color="blue">
                    <h1>产品标题</h1>
                    <p>产品描述包含<strong>重要信息</strong></p>
                    <ul>
                        <li>特性1</li>
                        <li>特性2</li>
                    </ul>
                    <table width="100%">
                        <tr><td>规格</td><td>值</td></tr>
                    </table>
                </font>
            </div>
            ''',
            "expected_features": ["多层嵌套处理", "保持结构完整性"]
        },
        {
            "name": "无HTML内容",
            "input": '这是纯文本内容，没有任何HTML标签',
            "expected_features": ["直接返回清理后文本"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print("-" * 60)
        print("输入:")
        print(test_case['input'][:200] + ('...' if len(test_case['input']) > 200 else ''))
        
        # 测试智能清理
        result = converter.clean_html_intelligently(test_case['input'])
        
        print("\n智能清理结果:")
        print(result[:300] + ('...' if len(result) > 300 else ''))
        
        # 测试基础清理对比
        basic_result = converter.clean_text(test_case['input'])
        print(f"\n基础清理结果:")
        print(basic_result[:200] + ('...' if len(basic_result) > 200 else ''))
        
        print(f"\n期望特性: {', '.join(test_case['expected_features'])}")
        
        # 简单验证
        if result and len(result) > 0:
            print("✅ 清理成功")
        else:
            print("❌ 清理失败")

def test_performance():
    """测试性能"""
    print("\n\n⚡ 性能测试")
    print("="*80)
    
    converter = EnhancedWooCommerceConverter()
    
    # 创建大量HTML内容
    large_html = '''
    <div style="background: #f0f0f0; padding: 20px;">
        <h1>产品标题</h1>
        <p>这是一个包含<strong>大量</strong>HTML内容的测试。</p>
        <ul>
            <li>特性 1</li>
            <li>特性 2</li>
            <li>特性 3</li>
        </ul>
        <table border="1">
            <tr><td>规格1</td><td>值1</td></tr>
            <tr><td>规格2</td><td>值2</td></tr>
        </table>
        <script>alert('危险代码')</script>
        <font color="red">旧式标签</font>
    </div>
    ''' * 10  # 重复10次
    
    import time
    
    # 测试智能清理性能
    start_time = time.time()
    for _ in range(100):
        result = converter.clean_html_intelligently(large_html)
    intelligent_time = time.time() - start_time
    
    # 测试基础清理性能
    start_time = time.time()
    for _ in range(100):
        result = converter.clean_text(large_html)
    basic_time = time.time() - start_time
    
    print(f"智能清理 100次耗时: {intelligent_time:.3f}秒")
    print(f"基础清理 100次耗时: {basic_time:.3f}秒")
    print(f"性能差异: {intelligent_time/basic_time:.1f}倍")

if __name__ == "__main__":
    test_html_cleaning()
    test_performance()
