#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的德国电商数据转换器 - 基于数据结构分析的最佳方案
"""

import pandas as pd
import re
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from optimal_sku_strategy import OptimalSKUGenerator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedDEConverter:
    def __init__(self):
        self.sku_generator = OptimalSKUGenerator("DE")
        
        # WooCommerce完整列定义
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
            'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
            'Brands', 'Meta: rank_math_focus_keyword'
        ]
        
        # 字段映射配置
        self.field_mappings = {
            'name_fields': ['title', 'post_title', 'Name', 'product_name', 'name'],
            'price_fields': ['price', 'regular_price', 'price/100', 'cost', 'Price', 'PRICE'],
            'sale_price_fields': ['sale_price', 'discount_price', 'special_price'],
            'description_fields': ['description', 'post_content', 'content', 'description s', 'long_description'],
            'short_desc_fields': ['short_description', 'post_excerpt', 'excerpt', 'summary'],
            'category_fields': ['category', 'categories', 'tax:product_cat', 'product_cat'],
            'image_fields': ['images', 'image', 'featured_image', 'product_image'],
            'brand_fields': ['brand', 'Brand', 'manufacturer', 'Manufacturer'],
            'sku_fields': ['sku', 'SKU', 'product_code', 'item_code'],
            'mpn_fields': ['mpn', 'MPN', 'MFG', 'model', 'part_number'],
            'upc_fields': ['UPC', 'upc', 'barcode', 'ean', 'gtin']
        }

    def detect_file_format(self, df: pd.DataFrame) -> str:
        """检测文件格式类型"""
        columns = [str(col).lower() for col in df.columns]
        
        if 'price/100' in columns:
            return 'bauhaus_format'
        elif 'description s' in columns:
            return 'bloomled_format'
        elif 'post_title' in columns:
            return 'wordpress_format'
        else:
            return 'generic_format'

    def get_field_value(self, row: pd.Series, field_list: List[str]) -> str:
        """从行数据中获取字段值"""
        for field in field_list:
            if field in row.index and pd.notna(row[field]) and str(row[field]).strip():
                return str(row[field]).strip()
        return ''

    def clean_html(self, html_content: str) -> str:
        """智能HTML清理"""
        if not html_content or str(html_content).strip() == 'nan':
            return ""
        
        content = str(html_content)
        
        # 智能标签替换
        replacements = {
            r'<h[12]([^>]*)>': r'<h3\1>',
            r'</h[12]>': '</h3>',
            r'<font([^>]*)>': r'<span\1>',
            r'</font>': '</span>',
            r'<center([^>]*)>': r'<div style="text-align: center;"\1>',
            r'</center>': '</div>'
        }
        
        for pattern, replacement in replacements.items():
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        # 移除危险标签
        dangerous_tags = ['script', 'style', 'iframe', 'object', 'embed']
        for tag in dangerous_tags:
            content = re.sub(f'<{tag}[^>]*>.*?</{tag}>', '', content, flags=re.IGNORECASE | re.DOTALL)
        
        # 移除事件属性
        content = re.sub(r'\s*on\w+\s*=\s*["\'][^"\']*["\']', '', content, flags=re.IGNORECASE)
        
        return content.strip()

    def process_price(self, price_value: str, file_format: str) -> float:
        """处理价格字段"""
        if pd.isna(price_value) or not str(price_value).strip():
            return 0.0
        
        try:
            price = float(str(price_value).replace(',', '.'))
            
            # 特殊格式处理
            if file_format == 'bauhaus_format':
                price = price / 100  # price/100 字段需要除以100
            
            return round(price, 2)
        except:
            return 0.0

    def calculate_sale_price(self, regular_price: float) -> float:
        """计算促销价格 - 分层策略"""
        if regular_price <= 0:
            return 0.0
        
        import random
        
        # 分层价格策略
        if regular_price <= 50:
            # 低价商品：30-60%折扣
            discount_factor = random.uniform(0.4, 0.7)
        elif regular_price <= 200:
            # 中价商品：20-40%折扣
            discount_factor = random.uniform(0.6, 0.8)
        elif regular_price <= 500:
            # 高价商品：10-25%折扣
            discount_factor = random.uniform(0.75, 0.9)
        else:
            # 奢侈品：5-15%折扣
            discount_factor = random.uniform(0.85, 0.95)
        
        sale_price = regular_price * discount_factor
        return round(sale_price, 2)

    def process_categories(self, category_str: str) -> str:
        """处理分类字段"""
        if pd.isna(category_str) or not str(category_str).strip():
            return "Uncategorized"
        
        categories = str(category_str).strip()
        
        # 处理不同的分隔符
        if '>' in categories:
            parts = [part.strip() for part in categories.split('>') if part.strip()]
        elif '|||' in categories:
            parts = [part.strip() for part in categories.split('|||') if part.strip()]
        elif ',' in categories:
            parts = [part.strip() for part in categories.split(',') if part.strip()]
        else:
            parts = [categories]
        
        # 清理和去重
        cleaned_parts = []
        seen = set()
        for part in parts:
            if part and part.lower() not in seen:
                cleaned_parts.append(part)
                seen.add(part.lower())
        
        return ' > '.join(cleaned_parts) if cleaned_parts else "Uncategorized"

    def convert_row_to_wc(self, row: pd.Series, file_format: str, first_col_analysis: Dict) -> Dict:
        """将行数据转换为WooCommerce格式"""
        wc_row = {}
        
        # 基础字段
        wc_row['ID'] = ''
        wc_row['Type'] = 'simple'
        
        # 使用最优SKU生成策略
        wc_row['SKU'] = self.sku_generator.generate_optimal_sku(row, first_col_analysis)
        
        # 产品名称
        name = self.get_field_value(row, self.field_mappings['name_fields'])
        wc_row['Name'] = self.clean_html(name)
        
        # 价格处理
        price_value = self.get_field_value(row, self.field_mappings['price_fields'])
        regular_price = self.process_price(price_value, file_format)
        sale_price = self.calculate_sale_price(regular_price)
        
        wc_row['Regular price'] = regular_price
        wc_row['Sale price'] = sale_price
        
        # 描述
        description = self.get_field_value(row, self.field_mappings['description_fields'])
        short_desc = self.get_field_value(row, self.field_mappings['short_desc_fields'])
        
        wc_row['Description'] = self.clean_html(description)
        wc_row['Short description'] = self.clean_html(short_desc)
        
        # 分类
        category = self.get_field_value(row, self.field_mappings['category_fields'])
        wc_row['Categories'] = self.process_categories(category)
        wc_row['Tags'] = wc_row['Categories'].split(' > ')[-1] if wc_row['Categories'] != "Uncategorized" else ""
        
        # 图片
        image = self.get_field_value(row, self.field_mappings['image_fields'])
        wc_row['Images'] = image
        
        # 其他必需字段
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        wc_row['In stock?'] = 1
        wc_row['Stock'] = 999
        wc_row['Low stock amount'] = ''
        wc_row['Backorders allowed?'] = 0
        wc_row['Sold individually?'] = 0
        wc_row['Weight (kg)'] = ''
        wc_row['Length (cm)'] = ''
        wc_row['Width (cm)'] = ''
        wc_row['Height (cm)'] = ''
        wc_row['Allow customer reviews?'] = 1
        wc_row['Purchase note'] = ''
        wc_row['Date sale price starts'] = ''
        wc_row['Date sale price ends'] = ''
        wc_row['Shipping class'] = ''
        
        # 属性
        brand = self.get_field_value(row, self.field_mappings['brand_fields'])
        wc_row['Attribute 1 name'] = 'Brand'
        wc_row['Attribute 1 value(s)'] = brand
        wc_row['Attribute 1 visible'] = 1
        wc_row['Attribute 1 global'] = 0
        
        # 其他空字段
        for col in self.wc_columns:
            if col not in wc_row:
                wc_row[col] = ''
        
        return wc_row

    def process_file(self, input_file: Path) -> bool:
        """处理单个文件"""
        try:
            logger.info(f"开始处理文件: {input_file.name}")
            
            # 读取Excel文件
            df = pd.read_excel(input_file)
            logger.info(f"读取到 {len(df)} 行数据")
            
            # 检测文件格式
            file_format = self.detect_file_format(df)
            logger.info(f"检测到文件格式: {file_format}")
            
            # 分析第一列
            first_col_analysis = self.sku_generator.analyze_first_column_suitability(df)
            logger.info(f"第一列分析: {first_col_analysis['column_name']} (评分: {first_col_analysis['score']}/10, 适合: {first_col_analysis['suitable']})")
            
            # 转换数据
            wc_rows = []
            for index, row in df.iterrows():
                if index % 1000 == 0:
                    logger.info(f"已处理 {index} 行...")
                
                wc_row = self.convert_row_to_wc(row, file_format, first_col_analysis)
                wc_rows.append(wc_row)
            
            # 保存结果
            output_dir = Path("woocommerce_output_improved")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / f"{input_file.stem}_improved.csv"
            
            wc_df = pd.DataFrame(wc_rows, columns=self.wc_columns)
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            logger.info(f"转换完成，保存到: {output_file}")
            logger.info(f"成功转换 {len(wc_rows)} 行数据")
            
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return False

def main():
    """主函数"""
    converter = ImprovedDEConverter()
    
    # 德国数据文件路径
    source_dir = Path("源数据文件/de")
    
    if not source_dir.exists():
        logger.error("德国数据目录不存在!")
        return
    
    # 获取所有Excel文件
    excel_files = [f for f in source_dir.glob("*.xlsx") if not f.name.startswith('~')]
    
    if not excel_files:
        logger.error("没有找到Excel文件!")
        return
    
    logger.info(f"找到 {len(excel_files)} 个Excel文件")
    
    # 处理文件
    success_count = 0
    for i, file in enumerate(excel_files[:3], 1):  # 先处理前3个文件测试
        logger.info(f"\n[{i}/{min(3, len(excel_files))}] 处理文件...")
        if converter.process_file(file):
            success_count += 1
    
    logger.info(f"\n处理完成! 成功: {success_count}/{min(3, len(excel_files))}")

if __name__ == "__main__":
    main()
