#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析源数据文件结构
"""

import pandas as pd
import os
from pathlib import Path

def analyze_excel_file(file_path):
    """分析单个Excel文件"""
    try:
        print(f"\n{'='*60}")
        print(f"分析文件: {file_path.name}")
        print(f"{'='*60}")
        
        # 读取前几行数据
        df = pd.read_excel(file_path, nrows=5)
        
        print(f"文件大小: {file_path.stat().st_size / 1024 / 1024:.2f} MB")
        print(f"列数: {len(df.columns)}")
        print(f"样本行数: {len(df)}")
        
        print(f"\n列名列表:")
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. {col}")
        
        # 检查关键字段
        key_fields = {
            '产品名称': ['title', 'name', 'post_title', 'product_name', 'Name'],
            '价格': ['price', 'regular_price', 'sale_price', 'cost', 'Price'],
            '描述': ['description', 'post_content', 'desc', 'Description'],
            '分类': ['category', 'categories', 'tax:product_cat', 'Categories'],
            '图片': ['images', 'image', 'featured', 'photo', 'Images'],
            'SKU': ['sku', 'SKU', 'product_id', 'id'],
            '品牌': ['brand', 'Brand', 'attribute:Brand', 'manufacturer']
        }
        
        print(f"\n关键字段检测:")
        found_fields = {}
        for field_type, possible_names in key_fields.items():
            found = []
            for col in df.columns:
                if any(name.lower() in str(col).lower() for name in possible_names):
                    found.append(col)
            if found:
                found_fields[field_type] = found
                print(f"✅ {field_type}: {found}")
            else:
                print(f"❌ {field_type}: 未找到")
        
        # 显示前2行数据样本
        print(f"\n前2行数据样本:")
        if len(df) > 0:
            for i in range(min(2, len(df))):
                print(f"\n--- 第{i+1}行 ---")
                for col in df.columns[:10]:  # 只显示前10列
                    value = str(df.iloc[i][col])[:100]  # 限制长度
                    print(f"{col}: {value}")
                if len(df.columns) > 10:
                    print(f"... 还有 {len(df.columns) - 10} 列")
        
        return found_fields
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return {}

def main():
    """主函数"""
    source_dir = Path("源数据文件/de")
    
    if not source_dir.exists():
        print("源数据目录不存在!")
        return
    
    # 获取所有Excel文件
    excel_files = list(source_dir.glob("*.xlsx"))
    excel_files = [f for f in excel_files if not f.name.startswith('~')]  # 排除临时文件
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 分析前3个文件
    all_fields = {}
    for i, file_path in enumerate(excel_files[:3]):
        fields = analyze_excel_file(file_path)
        all_fields[file_path.name] = fields
        
        if i < len(excel_files) - 1:
            input("\n按回车键继续分析下一个文件...")
    
    # 总结分析
    print(f"\n{'='*80}")
    print("总结分析")
    print(f"{'='*80}")
    
    # 统计字段出现频率
    field_count = {}
    for file_name, fields in all_fields.items():
        for field_type, columns in fields.items():
            if field_type not in field_count:
                field_count[field_type] = {}
            for col in columns:
                if col not in field_count[field_type]:
                    field_count[field_type][col] = 0
                field_count[field_type][col] += 1
    
    print("字段统计:")
    for field_type, columns in field_count.items():
        print(f"\n{field_type}:")
        for col, count in sorted(columns.items(), key=lambda x: x[1], reverse=True):
            print(f"  {col}: {count} 个文件")

if __name__ == "__main__":
    main()
