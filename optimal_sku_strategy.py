#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于数据结构分析的最佳SKU生成策略
"""

import pandas as pd
import re
from typing import Dict, List, Any, Optional

class OptimalSKUGenerator:
    def __init__(self, prefix: str = "DE"):
        self.sku_counter = 1
        self.sku_prefix = prefix
        self.used_skus = set()
        
        # 德语和英语停用词
        self.stop_words = {
            # 英语停用词
            'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            # 德语停用词
            'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'einen', 'einem',
            'einer', 'eines', 'und', 'oder', 'aber', 'mit', 'von', 'zu', 'auf', 'in',
            'an', 'bei', 'nach', 'vor', 'über', 'unter', 'durch', 'für', 'ohne',
            'gegen', 'um', 'bis', 'seit', 'während', 'wegen', 'trotz', 'statt'
        }

    def analyze_first_column_suitability(self, df: pd.DataFrame) -> Dict:
        """分析第一列是否适合作为SKU基础"""
        first_col = df.iloc[:, 0]
        first_col_name = df.columns[0].lower()
        
        analysis = {
            'column_name': df.columns[0],
            'score': 0,
            'reasons': [],
            'suitable': False
        }
        
        # 评分标准
        # 1. 列名包含ID相关关键词 (3分)
        id_keywords = ['id', 'sku', 'code', 'artikel', 'produkt', 'number']
        if any(keyword in first_col_name for keyword in id_keywords):
            analysis['score'] += 3
            analysis['reasons'].append("列名暗示这是ID字段")
        
        # 2. 所有值唯一 (3分)
        if first_col.nunique() == len(first_col):
            analysis['score'] += 3
            analysis['reasons'].append("所有值都是唯一的")
        
        # 3. 无空值 (2分)
        if not first_col.isnull().any():
            analysis['score'] += 2
            analysis['reasons'].append("无空值")
        
        # 4. 长度适中 (1分)
        sample_values = first_col.head().tolist()
        if all(len(str(val)) <= 20 for val in sample_values if pd.notna(val)):
            analysis['score'] += 1
            analysis['reasons'].append("长度适中")
        
        # 5. 格式一致 (1分)
        if len(set(type(val).__name__ for val in sample_values)) == 1:
            analysis['score'] += 1
            analysis['reasons'].append("数据类型一致")
        
        # 判断是否适合
        analysis['suitable'] = analysis['score'] >= 6
        
        return analysis

    def extract_meaningful_keywords(self, text: str, max_keywords: int = 2) -> List[str]:
        """提取有意义的关键词"""
        if not text or str(text).strip() == 'nan':
            return []
        
        # 清理文本
        clean_text = re.sub(r'[^\w\s\-]', ' ', str(text).lower())
        words = clean_text.split()
        
        # 过滤停用词、数字和短词
        keywords = []
        for word in words:
            if (len(word) > 2 and 
                word not in self.stop_words and 
                not word.isdigit() and
                len(keywords) < max_keywords):
                keywords.append(word)
        
        return keywords

    def generate_sku_from_first_column(self, first_col_value: Any) -> Optional[str]:
        """基于第一列生成SKU"""
        if pd.isna(first_col_value) or str(first_col_value).strip() == '':
            return None
        
        clean_value = str(first_col_value).strip()
        
        # 如果是数字，直接使用
        if clean_value.isdigit():
            sku = f"{self.sku_prefix}-{clean_value}"
        else:
            # 清理特殊字符
            clean_value = re.sub(r'[^\w\-]', '', clean_value)
            if len(clean_value) <= 12:
                sku = f"{self.sku_prefix}-{clean_value}"
            else:
                # 太长则截取
                sku = f"{self.sku_prefix}-{clean_value[:10]}-{self.sku_counter:03d}"
        
        # 确保唯一性
        if sku in self.used_skus:
            sku = f"{sku}-{self.sku_counter:03d}"
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:20]

    def generate_sku_from_name_brand(self, name: str, brand: str = "", category: str = "") -> str:
        """基于产品名称和品牌生成智能SKU"""
        components = [self.sku_prefix]
        
        # 品牌部分
        if brand and str(brand).strip() != 'nan':
            brand_clean = re.sub(r'[^\w]', '', str(brand))[:4].upper()
            if brand_clean:
                components.append(brand_clean)
        
        # 产品关键词部分
        if name and str(name).strip() != 'nan':
            keywords = self.extract_meaningful_keywords(name, 2)
            if keywords:
                # 每个关键词取前4个字符
                product_part = ''.join([word[:4].upper() for word in keywords])
                components.append(product_part[:8])  # 限制总长度
            else:
                # 兜底：使用产品名称前几个字符
                clean_name = re.sub(r'[^\w]', '', str(name))[:6].upper()
                if clean_name:
                    components.append(clean_name)
        
        # 分类部分（可选，如果SKU还不够长）
        if category and len('-'.join(components)) < 12:
            cat_clean = re.sub(r'[^\w]', '', str(category))[:3].upper()
            if cat_clean:
                components.append(cat_clean)
        
        # 序号部分
        components.append(f"{self.sku_counter:03d}")
        
        sku = '-'.join(components)
        
        # 长度控制
        if len(sku) > 20:
            # 简化版本
            if brand and str(brand).strip() != 'nan':
                brand_part = re.sub(r'[^\w]', '', str(brand))[:3].upper()
                name_part = re.sub(r'[^\w]', '', str(name))[:4].upper()
                sku = f"{self.sku_prefix}-{brand_part}-{name_part}-{self.sku_counter:03d}"
            else:
                name_part = re.sub(r'[^\w]', '', str(name))[:6].upper()
                sku = f"{self.sku_prefix}-{name_part}-{self.sku_counter:03d}"
        
        # 确保唯一性
        original_sku = sku
        counter = 1
        while sku in self.used_skus:
            sku = f"{original_sku[:-3]}{counter:03d}"
            counter += 1
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:20]

    def generate_optimal_sku(self, row: pd.Series, first_col_analysis: Dict) -> str:
        """最优SKU生成策略"""
        
        # 策略1: 如果第一列适合，优先使用
        if first_col_analysis['suitable']:
            first_col_sku = self.generate_sku_from_first_column(row.iloc[0])
            if first_col_sku:
                return first_col_sku
        
        # 策略2: 查找其他可能的ID字段
        id_fields = ['ID', 'id', 'sku', 'SKU', 'code', 'Code', 'product_id', 'artikel_nr']
        for field in id_fields:
            if field in row.index and pd.notna(row[field]):
                id_sku = self.generate_sku_from_first_column(row[field])
                if id_sku:
                    return id_sku
        
        # 策略3: 基于产品名称和品牌智能生成
        name_fields = ['Name', 'name', 'title', 'Title', 'post_title', 'product_name']
        brand_fields = ['Brand', 'brand', 'manufacturer', 'Marke', 'Hersteller']
        category_fields = ['Categories', 'category', 'tax:product_cat', 'Kategorie']
        
        name = ""
        brand = ""
        category = ""
        
        for field in name_fields:
            if field in row.index and pd.notna(row[field]):
                name = str(row[field])
                break
        
        for field in brand_fields:
            if field in row.index and pd.notna(row[field]):
                brand = str(row[field])
                break
        
        for field in category_fields:
            if field in row.index and pd.notna(row[field]):
                category = str(row[field])
                break
        
        return self.generate_sku_from_name_brand(name, brand, category)

def test_optimal_sku_generator():
    """测试最优SKU生成器"""
    generator = OptimalSKUGenerator("DE")
    
    # 模拟数据
    test_data = pd.DataFrame({
        'product_id': ['LAMP001', 'TOOL002', 'FURN003'],
        'Name': ['LED Ceiling Light Modern Design', 'Bosch Professional Drill Set', 'Outdoor Garden Table Set'],
        'Brand': ['Philips', 'Bosch', 'IKEA'],
        'Categories': ['Lighting > Ceiling', 'Tools > Power Tools', 'Garden > Furniture']
    })
    
    # 分析第一列
    first_col_analysis = generator.analyze_first_column_suitability(test_data)
    
    print("=== 第一列分析结果 ===")
    print(f"列名: {first_col_analysis['column_name']}")
    print(f"评分: {first_col_analysis['score']}/10")
    print(f"适合作为SKU: {first_col_analysis['suitable']}")
    print(f"原因: {', '.join(first_col_analysis['reasons'])}")
    
    print("\n=== SKU生成测试 ===")
    for index, row in test_data.iterrows():
        sku = generator.generate_optimal_sku(row, first_col_analysis)
        print(f"产品 {index+1}: {sku}")
        print(f"  名称: {row['Name']}")
        print(f"  品牌: {row['Brand']}")
        print()

if __name__ == "__main__":
    test_optimal_sku_generator()
