#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据结构分析脚本 - 快速了解数据文件结构
"""

import pandas as pd
from pathlib import Path

def quick_structure_check(file_path: str):
    """快速检查文件结构"""
    try:
        print(f"🔍 快速结构检查: {Path(file_path).name}")
        
        # 读取前几行
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path, nrows=3, encoding='utf-8-sig')
        else:
            df = pd.read_excel(file_path, nrows=3)
        
        print(f"列数: {len(df.columns)}")
        print(f"样本行数: {len(df)}")
        
        print(f"\n📋 列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        print(f"\n📊 第一行数据:")
        if len(df) > 0:
            row = df.iloc[0]
            for col in df.columns:
                value = str(row[col])
                if len(value) > 50:
                    value = value[:47] + "..."
                print(f"{col}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def batch_structure_check(directory: str):
    """批量检查目录中文件的结构"""
    dir_path = Path(directory)
    
    if not dir_path.exists():
        print(f"❌ 目录不存在: {directory}")
        return
    
    files = list(dir_path.glob("*.csv")) + list(dir_path.glob("*.xlsx"))
    
    if not files:
        print(f"❌ 目录中没有数据文件")
        return
    
    print(f"🚀 批量结构检查: {directory}")
    print(f"文件数量: {len(files)}")
    print("="*60)
    
    for i, file_path in enumerate(files, 1):
        print(f"\n[{i}/{len(files)}] " + "-"*40)
        quick_structure_check(str(file_path))

if __name__ == "__main__":
    print("📊 简单数据结构分析工具")
    print("="*40)
    
    # 检查当前目录的重要文件
    important_files = [
        "woocommerce_products.csv",
        "wc-simple.csv",
        "seo_results_output.csv"
    ]
    
    for file in important_files:
        if Path(file).exists():
            print(f"\n" + "="*50)
            quick_structure_check(file)
    
    # 检查input目录
    if Path("input").exists():
        print(f"\n" + "="*60)
        print("🔍 检查input目录中的文件结构")
        batch_structure_check("input")
