#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析源数据文件结构，特别是第一列的含义和SKU生成策略
"""

import pandas as pd
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_excel_structure(file_path):
    """分析Excel文件结构"""
    try:
        # 读取前10行数据
        df = pd.read_excel(file_path, nrows=10)
        
        result = {
            'file_name': file_path.name,
            'total_columns': len(df.columns),
            'column_names': list(df.columns),
            'first_column': {
                'name': df.columns[0],
                'sample_values': [],
                'data_type': str(df.dtypes[0]),
                'unique_count': df.iloc[:, 0].nunique(),
                'has_nulls': df.iloc[:, 0].isnull().any()
            }
        }
        
        # 获取第一列的样本值
        for i in range(min(5, len(df))):
            value = df.iloc[i, 0]
            result['first_column']['sample_values'].append({
                'row': i+1,
                'value': str(value),
                'type': type(value).__name__
            })
        
        # 分析第一列可能的用途
        first_col_name = df.columns[0].lower()
        if any(keyword in first_col_name for keyword in ['id', 'sku', 'code', 'number']):
            result['first_column']['likely_purpose'] = 'Product ID/SKU'
        elif any(keyword in first_col_name for keyword in ['name', 'title', 'product']):
            result['first_column']['likely_purpose'] = 'Product Name'
        elif any(keyword in first_col_name for keyword in ['price', 'cost', 'amount']):
            result['first_column']['likely_purpose'] = 'Price'
        else:
            result['first_column']['likely_purpose'] = 'Unknown'
        
        return result
        
    except Exception as e:
        logger.error(f"分析文件 {file_path} 失败: {e}")
        return None

def main():
    """主函数"""
    # 获取所有Excel文件
    excel_files = list(Path('.').glob('*.xlsx'))
    
    if not excel_files:
        logger.error("没有找到Excel文件!")
        return
    
    print("=" * 80)
    print("德国电商数据文件结构分析")
    print("=" * 80)
    
    for file_path in excel_files[:5]:  # 分析前5个文件
        print(f"\n📁 文件: {file_path.name}")
        print("-" * 60)
        
        result = analyze_excel_structure(file_path)
        if not result:
            continue
        
        print(f"总列数: {result['total_columns']}")
        print(f"前10列名: {result['column_names'][:10]}")
        
        first_col = result['first_column']
        print(f"\n🔍 第一列分析:")
        print(f"  列名: '{first_col['name']}'")
        print(f"  数据类型: {first_col['data_type']}")
        print(f"  可能用途: {first_col['likely_purpose']}")
        print(f"  唯一值数量: {first_col['unique_count']}")
        print(f"  包含空值: {first_col['has_nulls']}")
        
        print(f"\n📋 样本数据:")
        for sample in first_col['sample_values']:
            print(f"  第{sample['row']}行: {sample['value']} ({sample['type']})")
        
        # 检查是否适合作为SKU
        print(f"\n💡 SKU适用性评估:")
        if first_col['likely_purpose'] == 'Product ID/SKU':
            print("  ✅ 第一列很可能是产品ID/SKU，建议直接使用")
        elif first_col['unique_count'] == len(first_col['sample_values']):
            print("  ✅ 第一列值唯一，可以作为SKU使用")
        else:
            print("  ⚠️  第一列可能不适合直接作为SKU，建议生成新SKU")

if __name__ == "__main__":
    main()
