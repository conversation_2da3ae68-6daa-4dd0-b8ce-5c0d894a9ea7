#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析所有德国源数据文件的结构
"""

import pandas as pd
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_file_structure(file_path):
    """分析单个文件结构"""
    try:
        df = pd.read_excel(file_path, nrows=2)
        
        result = {
            'file': file_path.name,
            'columns': list(df.columns),
            'column_count': len(df.columns),
            'sample_data': {}
        }
        
        # 获取第一行数据样本
        if len(df) > 0:
            for col in df.columns[:10]:  # 只取前10列
                value = str(df.iloc[0][col])[:50]
                result['sample_data'][col] = value
        
        return result
        
    except Exception as e:
        logger.error(f"分析 {file_path.name} 失败: {e}")
        return None

def main():
    source_dir = Path("源数据文件/de")
    excel_files = [f for f in source_dir.glob("*.xlsx") if not f.name.startswith('~')]
    
    print(f"找到 {len(excel_files)} 个文件")
    
    all_results = []
    
    for file_path in excel_files[:5]:  # 分析前5个文件
        print(f"\n{'='*60}")
        print(f"分析: {file_path.name}")
        print(f"{'='*60}")
        
        result = analyze_file_structure(file_path)
        if result:
            all_results.append(result)
            
            print(f"列数: {result['column_count']}")
            print("列名:")
            for i, col in enumerate(result['columns']):
                print(f"  {i+1:2d}. {col}")
            
            print("\n第一行数据样本:")
            for col, value in list(result['sample_data'].items())[:8]:
                print(f"  {col}: {value}")
    
    # 统计字段出现频率
    print(f"\n{'='*80}")
    print("字段统计分析")
    print(f"{'='*80}")
    
    field_count = {}
    for result in all_results:
        for col in result['columns']:
            if col not in field_count:
                field_count[col] = []
            field_count[col].append(result['file'])
    
    print("字段出现频率:")
    for field, files in sorted(field_count.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{field}: {len(files)} 个文件 - {files}")

if __name__ == "__main__":
    main()
