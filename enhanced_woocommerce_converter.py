#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版WooCommerce转换脚本
功能：
1. 基于产品名和图片链接的重复产品检测和去重
2. 支持每份CSV输出5万个产品
3. 完整的变体产品支持
4. 全面的数据处理逻辑
"""

import pandas as pd
import numpy as np
import re
import hashlib
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import defaultdict
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedWooCommerceConverter:
    def __init__(self, config_file: str = "converter_config.json"):
        """初始化转换器"""
        self.sku_counter = 1
        self.sku_prefix = "WC"
        self.products_per_file = 50000  # 每份CSV 5万个产品
        self.seen_products = set()  # 用于去重的产品集合
        self.duplicate_count = 0
        self.processed_count = 0
        
        # 加载配置
        self.config = self.load_config(config_file)
        
        # WooCommerce完整列定义
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
            'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
            'Attribute 5 name', 'Attribute 5 value(s)', 'Attribute 5 visible', 'Attribute 5 global',
            'Attribute 6 name', 'Attribute 6 value(s)', 'Attribute 6 visible', 'Attribute 6 global',
            'Brands', 'Meta: rank_math_focus_keyword'
        ]
        
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "deduplication": {
                "enabled": True,
                "method": "name_image",  # name_image, sku, name_only
                "similarity_threshold": 0.9
            },
            "output": {
                "products_per_file": 50000,
                "file_prefix": "woocommerce_products",
                "include_variations": True
            },
            "product_processing": {
                "auto_generate_sku": True,
                "price_adjustment": True,
                "html_cleanup": True,
                "image_optimization": True
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def generate_product_hash(self, name: str, image_url: str) -> str:
        """基于产品名和图片链接生成唯一哈希"""
        # 清理产品名称
        clean_name = re.sub(r'[^\w\s]', '', str(name).lower().strip())
        clean_name = re.sub(r'\s+', ' ', clean_name)
        
        # 提取图片URL的主要部分（去除参数）
        clean_image = str(image_url).split('?')[0].split(',')[0].strip() if image_url else ""
        
        # 生成组合哈希
        combined = f"{clean_name}|{clean_image}"
        return hashlib.md5(combined.encode('utf-8')).hexdigest()
    
    def is_duplicate_product(self, row: Dict) -> bool:
        """检查是否为重复产品"""
        if not self.config["deduplication"]["enabled"]:
            return False
            
        name = str(row.get('Name', row.get('post_title', ''))).strip()
        images = str(row.get('Images', row.get('images', ''))).strip()
        
        if not name:  # 如果没有产品名称，跳过
            return True
            
        product_hash = self.generate_product_hash(name, images)
        
        if product_hash in self.seen_products:
            self.duplicate_count += 1
            logger.debug(f"发现重复产品: {name[:50]}...")
            return True
        
        self.seen_products.add(product_hash)
        return False
    
    def detect_product_type(self, row: Dict) -> str:
        """检测产品类型（simple, variable, variation）"""
        # 检查是否有父产品ID
        parent_id = str(row.get('Parent', '')).strip()
        if parent_id and parent_id != '0':
            return 'variation'
        
        # 检查是否有变体属性
        has_variations = False
        for i in range(1, 7):  # 检查6个属性字段
            attr_name = str(row.get(f'Attribute {i} name', '')).strip()
            attr_values = str(row.get(f'Attribute {i} value(s)', '')).strip()
            
            if attr_name and attr_values:
                # 如果属性值包含多个选项（用|或,分隔），可能是变体产品
                if '|' in attr_values or ',' in attr_values:
                    has_variations = True
                    break
        
        return 'variable' if has_variations else 'simple'
    
    def process_variation_attributes(self, row: Dict) -> List[Dict]:
        """处理变体属性，生成变体产品"""
        variations = []
        base_product = dict(row)
        
        # 查找有多个值的属性
        variation_attributes = {}
        for i in range(1, 7):
            attr_name = str(row.get(f'Attribute {i} name', '')).strip()
            attr_values = str(row.get(f'Attribute {i} value(s)', '')).strip()
            
            if attr_name and attr_values:
                if '|' in attr_values:
                    values = [v.strip() for v in attr_values.split('|') if v.strip()]
                elif ',' in attr_values:
                    values = [v.strip() for v in attr_values.split(',') if v.strip()]
                else:
                    values = [attr_values]
                
                if len(values) > 1:
                    variation_attributes[attr_name] = values
        
        if not variation_attributes:
            return [base_product]  # 没有变体，返回原产品
        
        # 生成变体组合
        import itertools
        attr_names = list(variation_attributes.keys())
        attr_values = list(variation_attributes.values())
        
        # 创建父产品
        parent_product = dict(base_product)
        parent_product['Type'] = 'variable'
        parent_product['SKU'] = self.generate_sku_optimized(
            parent_product.get('Name', ''), 
            parent_product.get('Brands', '')
        )
        variations.append(parent_product)
        
        # 生成所有变体组合
        for combination in itertools.product(*attr_values):
            variation = dict(base_product)
            variation['Type'] = 'variation'
            variation['Parent'] = parent_product['SKU']
            
            # 更新变体名称
            variation_suffix = ' - ' + ', '.join(combination)
            variation['Name'] = base_product.get('Name', '') + variation_suffix
            
            # 生成变体SKU
            variation['SKU'] = self.generate_sku_optimized(
                variation['Name'], 
                variation.get('Brands', '')
            )
            
            # 设置变体属性
            for j, (attr_name, attr_value) in enumerate(zip(attr_names, combination)):
                attr_index = j + 1
                variation[f'Attribute {attr_index} name'] = attr_name
                variation[f'Attribute {attr_index} value(s)'] = attr_value
                variation[f'Attribute {attr_index} visible'] = 1
                variation[f'Attribute {attr_index} global'] = 0
            
            variations.append(variation)
        
        return variations
    
    def generate_sku_optimized(self, product_name: str, brand: str = "") -> str:
        """生成优化的SKU"""
        # 清理产品名称，提取关键词
        clean_name = re.sub(r'[^\w\s]', '', str(product_name))
        words = clean_name.split()
        
        # 提取关键产品词（排除常见词）
        stop_words = {'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an', 'is', 'are'}
        key_words = [word for word in words if word.lower() not in stop_words and len(word) > 2]
        
        # 取前2-3个关键词，每个词取前3-4个字符
        product_part = ""
        for word in key_words[:3]:
            product_part += word[:4].upper()
            if len(product_part) >= 8:
                break
        
        # 品牌处理
        brand_part = ""
        if brand and brand.strip():
            clean_brand = re.sub(r'[^\w]', '', str(brand))[:3].upper()
            brand_part = f"{clean_brand}-"
        
        # 生成SKU
        sku = f"{self.sku_prefix}-{brand_part}{product_part}-{self.sku_counter:04d}"
        
        # 确保SKU不超过20个字符
        if len(sku) > 20:
            sku = f"{self.sku_prefix}-{self.sku_counter:05d}"
        
        self.sku_counter += 1
        return sku
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if pd.isna(text) or text == '':
            return ''
        
        text = str(text)
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 清理特殊字符，保留基本标点
        text = re.sub(r'[^\w\s\-\.\,\|\&\#\(\)\[\]\'\":]', '', text)
        # 清理多余空格
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def process_images(self, images_str: str, featured_str: str = "") -> str:
        """处理复杂的图片字段，支持多种格式和去重"""
        if pd.isna(images_str) or images_str == '':
            if pd.isna(featured_str) or featured_str == '':
                return ''
            return str(featured_str).strip()

        images = str(images_str).strip()

        # 如果没有图片数据，返回空
        if not images or images.lower() in ['nan', 'null', 'none']:
            return ''

        # 提取所有可能的图片URL
        image_urls = self.extract_image_urls(images)

        # 去重并验证URL
        unique_urls = self.deduplicate_and_validate_urls(image_urls)

        # 限制图片数量
        max_images = self.config.get("product_processing", {}).get("max_images_per_product", 5)
        final_urls = unique_urls[:max_images]

        return ','.join(final_urls) if final_urls else ''

    def extract_image_urls(self, images_str: str) -> List[str]:
        """从复杂的图片字符串中提取所有URL"""
        urls = []
        original_str = images_str

        # 第一步：使用正则表达式直接提取所有可能的URL
        # 更宽松的URL匹配模式，包括各种可能的格式
        url_patterns = [
            # 标准的图片URL模式
            r'https?://[^\s<>"\'|,;]+\.(jpg|jpeg|png|gif|webp|bmp|svg)(?:\?[^\s<>"\'|,;]*)?',
            # 更宽松的模式，匹配可能的图片URL
            r'https?://[^\s<>"\'|,;]+/[^\s<>"\'|,;]*\.(jpg|jpeg|png|gif|webp|bmp|svg)',
            # 匹配包含图片路径但可能没有扩展名的URL
            r'https?://[^\s<>"\'|,;]*(?:image|img|photo|pic)[^\s<>"\'|,;]*',
        ]

        for pattern in url_patterns:
            for match in re.finditer(pattern, original_str, re.IGNORECASE):
                url = match.group(0)
                # 清理URL末尾可能的引号或其他字符
                url = re.sub(r'["\'>]+$', '', url)
                if url and self.is_valid_image_url(url):
                    urls.append(url)

        # 第二步：如果没有找到URL，尝试清理HTML并重新提取
        if not urls:
            # 清理HTML标签和属性
            clean_str = original_str

            # 提取srcset属性中的URL
            srcset_pattern = r'srcset\s*=\s*["\']([^"\']+)["\']'
            for match in re.finditer(srcset_pattern, clean_str, re.IGNORECASE):
                srcset_value = match.group(1)
                # srcset可能包含多个URL，用逗号分隔
                for url in srcset_value.split(','):
                    url = url.strip().split(' ')[0]  # 移除可能的尺寸描述符
                    if self.is_valid_image_url(url):
                        urls.append(url)

            # 移除HTML标签
            clean_str = re.sub(r'<[^>]*>', '', clean_str)

            # 移除HTML属性
            clean_str = re.sub(r'\b(srcset|type|src)\s*=\s*["\']?', '', clean_str)
            clean_str = re.sub(r'["\']?\s*(type|image/webp|image/jpg|image/jpeg|image/png)', '', clean_str)

            # 再次尝试提取URL
            for pattern in url_patterns:
                for match in re.finditer(pattern, clean_str, re.IGNORECASE):
                    url = match.group(0)
                    url = re.sub(r'["\'>]+$', '', url)
                    if url and self.is_valid_image_url(url):
                        urls.append(url)

        # 第三步：如果还是没有找到，尝试按分隔符分割
        if not urls:
            separators = ['|||', '|', ',', ';', '\n', '\r\n', '><']

            for separator in separators:
                if separator in original_str:
                    parts = original_str.split(separator)
                    for part in parts:
                        part = part.strip().strip('"\'<>')
                        # 清理可能的HTML属性
                        part = re.sub(r'^[^h]*?(https?://)', r'\1', part)
                        if part and self.is_valid_image_url(part):
                            urls.append(part)
                    if urls:  # 如果找到了URL，就不再尝试其他分隔符
                        break

        # 第四步：最后尝试，检查整个字符串是否就是一个URL
        if not urls:
            cleaned = original_str.strip().strip('"\'<>')
            cleaned = re.sub(r'^[^h]*?(https?://)', r'\1', cleaned)
            if self.is_valid_image_url(cleaned):
                urls.append(cleaned)

        return urls

    def is_valid_image_url(self, url: str) -> bool:
        """验证是否为有效的图片URL"""
        if not url or len(url) < 10:
            return False

        # 检查是否以http开头
        if not url.startswith(('http://', 'https://')):
            return False

        # 检查是否包含图片扩展名
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
        url_lower = url.lower()

        # 检查URL中是否包含图片扩展名（可能在查询参数之前）
        url_without_params = url_lower.split('?')[0]
        has_image_ext = any(url_without_params.endswith(ext) for ext in image_extensions)

        # 或者检查URL中是否包含图片相关的路径
        has_image_path = any(ext.replace('.', '') in url_lower for ext in image_extensions)

        return has_image_ext or has_image_path

    def deduplicate_and_validate_urls(self, urls: List[str]) -> List[str]:
        """去重并验证图片URL"""
        if not urls:
            return []

        unique_urls = []
        seen_urls = set()
        seen_filenames = set()

        for url in urls:
            url = url.strip().strip('"\'')

            # 跳过空URL
            if not url:
                continue

            # 跳过已经见过的完整URL
            if url in seen_urls:
                continue

            # 验证URL格式
            if not self.is_valid_image_url(url):
                continue

            # 提取文件名进行去重（处理同一图片的不同尺寸版本）
            filename = self.extract_image_filename(url)
            if filename in seen_filenames:
                continue

            unique_urls.append(url)
            seen_urls.add(url)
            seen_filenames.add(filename)

        return unique_urls

    def extract_image_filename(self, url: str) -> str:
        """从URL中提取图片文件名用于去重"""
        try:
            # 移除查询参数
            url_without_params = url.split('?')[0]

            # 提取文件名
            filename = url_without_params.split('/')[-1]

            # 移除可能的尺寸标识符（如 _thumb, _small, _large, 数字等）
            base_filename = re.sub(r'[_-](thumb|small|large|medium|\d+x?\d*)', '', filename, flags=re.IGNORECASE)

            return base_filename.lower()
        except:
            return url.lower()

    def process_categories(self, categories_str: str) -> Tuple[str, str]:
        """处理分类字段，返回(分类, 标签)"""
        if pd.isna(categories_str) or categories_str == '':
            return "Uncategorized", "General"

        categories = str(categories_str).strip()

        # 处理各种分隔符
        if ' > ' in categories:
            parts = [p.strip() for p in categories.split(' > ') if p.strip()]
        elif '|||' in categories:
            parts = [p.strip() for p in categories.split('|||') if p.strip()]
        elif ',' in categories:
            parts = [p.strip() for p in categories.split(',') if p.strip()]
        else:
            parts = [categories]

        # 清理和去重
        cleaned_parts = []
        seen = set()
        for part in parts:
            clean_part = self.clean_text(part)
            if clean_part and clean_part.lower() not in seen and len(cleaned_parts) < 4:
                cleaned_parts.append(clean_part)
                seen.add(clean_part.lower())

        if not cleaned_parts:
            return "Uncategorized", "General"

        # 构建分类字符串
        category_str = ' > '.join(cleaned_parts)

        # 生成标签（使用最后一个分类）
        tags = cleaned_parts[-1] if cleaned_parts else "General"

        return category_str, tags

    def calculate_prices(self, original_price: float) -> Tuple[float, float]:
        """计算常规价格和促销价格"""
        if pd.isna(original_price) or original_price <= 0:
            return 0.0, 0.0

        # 常规价格调整
        if original_price < 3:
            regular_price = round(original_price, 2)
        elif original_price <= 10:
            regular_price = round(original_price - 1, 2)
        elif original_price <= 50:
            regular_price = round(original_price - 2, 2)
        else:
            regular_price = round(original_price - 3, 2)

        regular_price = max(regular_price, 0.01)

        # 促销价格计算
        if regular_price <= 50:
            sale_price = regular_price * 0.56
        elif regular_price <= 100:
            sale_price = regular_price * 0.5
        elif regular_price <= 150:
            sale_price = regular_price * 0.45
        elif regular_price <= 200:
            sale_price = regular_price * 0.42
        elif regular_price <= 250:
            sale_price = regular_price * 0.36
        elif regular_price <= 300:
            sale_price = regular_price * 0.33
        elif regular_price <= 500:
            sale_price = regular_price * 0.26
        elif regular_price <= 1000:
            sale_price = regular_price * 0.22
        else:
            sale_price = regular_price * 0.15

        return regular_price, round(sale_price, 2)

    def convert_row_to_wc(self, row: Dict) -> List[Dict]:
        """将源数据行转换为WooCommerce格式，支持变体产品"""
        # 检查重复
        if self.is_duplicate_product(row):
            return []

        # 检测产品类型
        product_type = self.detect_product_type(row)

        # 处理变体产品
        if product_type == 'variable' and self.config["output"]["include_variations"]:
            variations = self.process_variation_attributes(row)
            return [self.build_wc_product(var_row) for var_row in variations]
        else:
            return [self.build_wc_product(row)]

    def build_wc_product(self, row: Dict) -> Dict:
        """构建单个WooCommerce产品"""
        wc_row = {}

        # 基础字段
        wc_row['ID'] = ''
        wc_row['Type'] = row.get('Type', 'simple')
        wc_row['SKU'] = row.get('SKU', '') or self.generate_sku_optimized(
            row.get('Name', row.get('post_title', '')),
            row.get('Brands', row.get('attribute:Brand', ''))
        )
        wc_row['Name'] = self.clean_text(row.get('Name', row.get('post_title', '')))
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'

        # 描述字段
        wc_row['Short description'] = self.clean_text(row.get('Short description', row.get('post_excerpt', '')))
        wc_row['Description'] = self.clean_text(row.get('Description', row.get('post_content', '')))

        # 价格处理
        original_price = pd.to_numeric(row.get('Regular price', row.get('regular_price', 0)), errors='coerce')
        regular_price, sale_price = self.calculate_prices(original_price)
        wc_row['Regular price'] = regular_price
        wc_row['Sale price'] = sale_price

        # 库存字段
        wc_row['In stock?'] = 1 if str(row.get('stock_status', row.get('In stock?', ''))).lower() in ['instock', '1', 'true'] else 0
        wc_row['Stock'] = self.clean_text(row.get('Stock', ''))
        wc_row['Low stock amount'] = ''
        wc_row['Backorders allowed?'] = 1
        wc_row['Sold individually?'] = 0

        # 尺寸重量
        wc_row['Weight (kg)'] = self.clean_text(row.get('Weight (kg)', row.get('weight', '')))
        wc_row['Length (cm)'] = self.clean_text(row.get('Length (cm)', row.get('length', '')))
        wc_row['Width (cm)'] = self.clean_text(row.get('Width (cm)', row.get('width', '')))
        wc_row['Height (cm)'] = self.clean_text(row.get('Height (cm)', row.get('height', '')))

        # 分类和标签
        categories, tags = self.process_categories(row.get('Categories', row.get('tax:product_cat', '')))
        wc_row['Categories'] = categories
        wc_row['Tags'] = tags

        # 图片
        wc_row['Images'] = self.process_images(
            row.get('Images', row.get('images', '')),
            row.get('featured', '')
        )

        # 其他字段
        wc_row['Allow customer reviews?'] = 1
        wc_row['Purchase note'] = ''
        wc_row['Shipping class'] = ''
        wc_row['Date sale price starts'] = ''
        wc_row['Date sale price ends'] = ''
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        wc_row['Download limit'] = ''
        wc_row['Download expiry days'] = ''
        wc_row['Parent'] = row.get('Parent', '')
        wc_row['Grouped products'] = ''
        wc_row['Upsells'] = ''
        wc_row['Cross-sells'] = ''
        wc_row['External URL'] = ''
        wc_row['Button text'] = ''
        wc_row['Position'] = 0

        # 属性字段（支持6个属性）
        for i in range(1, 7):
            wc_row[f'Attribute {i} name'] = self.clean_text(row.get(f'Attribute {i} name', ''))
            wc_row[f'Attribute {i} value(s)'] = self.clean_text(row.get(f'Attribute {i} value(s)', ''))
            wc_row[f'Attribute {i} visible'] = row.get(f'Attribute {i} visible', 1)
            wc_row[f'Attribute {i} global'] = row.get(f'Attribute {i} global', 0)

        # 品牌和SEO
        wc_row['Brands'] = self.clean_text(row.get('Brands', row.get('attribute:Brand', '')))
        wc_row['Meta: rank_math_focus_keyword'] = ''

        return wc_row

    def process_file(self, input_file: str, output_dir: str) -> bool:
        """处理单个输入文件"""
        try:
            logger.info(f"开始处理文件: {input_file}")

            # 读取文件
            if input_file.endswith('.xlsx') or input_file.endswith('.xls'):
                df = pd.read_excel(input_file)
            else:
                df = pd.read_csv(input_file, encoding='utf-8-sig')

            logger.info(f"读取到 {len(df)} 行数据")

            # 重置计数器
            self.processed_count = 0
            self.duplicate_count = 0

            # 处理数据
            all_products = []
            for index, row in df.iterrows():
                if index % 1000 == 0:
                    logger.info(f"已处理 {index} 行...")

                products = self.convert_row_to_wc(row.to_dict())
                all_products.extend(products)
                self.processed_count += len(products)

            logger.info(f"转换完成，共生成 {len(all_products)} 个产品")
            logger.info(f"跳过重复产品: {self.duplicate_count} 个")

            # 分割输出文件
            self.save_products_to_files(all_products, output_dir, input_file)

            return True

        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return False

    def save_products_to_files(self, products: List[Dict], output_dir: str, source_file: str):
        """将产品保存到多个文件，每个文件最多5万个产品"""
        if not products:
            logger.warning("没有产品需要保存")
            return

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 获取源文件名（不含扩展名）
        source_name = Path(source_file).stem

        # 分割产品列表
        total_files = (len(products) + self.products_per_file - 1) // self.products_per_file

        for file_index in range(total_files):
            start_idx = file_index * self.products_per_file
            end_idx = min(start_idx + self.products_per_file, len(products))

            file_products = products[start_idx:end_idx]

            # 生成文件名
            if total_files == 1:
                output_file = output_path / f"{source_name}_woocommerce.csv"
            else:
                output_file = output_path / f"{source_name}_woocommerce_part_{file_index + 1}.csv"

            # 创建DataFrame并保存
            df = pd.DataFrame(file_products, columns=self.wc_columns)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')

            logger.info(f"保存文件: {output_file} ({len(file_products)} 个产品)")

    def process_directory(self, input_dir: str, output_dir: str):
        """处理整个目录的文件"""
        input_path = Path(input_dir)
        if not input_path.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return

        # 获取所有支持的文件
        supported_extensions = ['.csv', '.xlsx', '.xls']
        input_files = []

        for ext in supported_extensions:
            input_files.extend(input_path.glob(f"*{ext}"))

        if not input_files:
            logger.error("没有找到支持的文件")
            return

        logger.info(f"找到 {len(input_files)} 个文件:")
        for file in input_files:
            logger.info(f"  - {file.name}")

        # 处理每个文件
        success_count = 0
        for file in input_files:
            logger.info(f"\n{'='*60}")
            logger.info(f"处理文件: {file.name}")
            logger.info(f"{'='*60}")

            if self.process_file(str(file), output_dir):
                success_count += 1
                logger.info(f"✓ {file.name} 处理成功")
            else:
                logger.error(f"✗ {file.name} 处理失败")

        logger.info(f"\n处理完成! 成功: {success_count}/{len(input_files)}")

        # 输出统计信息
        logger.info(f"总处理产品数: {self.processed_count}")
        logger.info(f"跳过重复产品: {self.duplicate_count}")
        logger.info(f"去重率: {self.duplicate_count / (self.processed_count + self.duplicate_count) * 100:.2f}%")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='增强版WooCommerce转换脚本')
    parser.add_argument('-i', '--input', required=True, help='输入文件或目录路径')
    parser.add_argument('-o', '--output', default='woocommerce_output', help='输出目录路径')
    parser.add_argument('-c', '--config', default='converter_config.json', help='配置文件路径')
    parser.add_argument('--products-per-file', type=int, default=50000, help='每个文件的产品数量')

    args = parser.parse_args()

    # 创建转换器
    converter = EnhancedWooCommerceConverter(args.config)
    converter.products_per_file = args.products_per_file

    # 处理输入
    input_path = Path(args.input)

    if input_path.is_file():
        # 处理单个文件
        converter.process_file(str(input_path), args.output)
    elif input_path.is_dir():
        # 处理目录
        converter.process_directory(str(input_path), args.output)
    else:
        logger.error(f"输入路径不存在: {args.input}")

if __name__ == "__main__":
    main()
