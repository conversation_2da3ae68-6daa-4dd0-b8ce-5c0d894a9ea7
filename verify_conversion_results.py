#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证转换结果的质量
"""

import pandas as pd
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_woocommerce_csv(csv_file):
    """验证WooCommerce CSV文件质量"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        result = {
            'file_name': csv_file.name,
            'total_products': len(df),
            'file_size_mb': round(csv_file.stat().st_size / 1024 / 1024, 2),
            'issues': [],
            'quality_score': 0,
            'sample_data': {}
        }
        
        # 检查必需字段
        required_fields = ['SKU', 'Name', 'Regular price', 'Categories', 'Type']
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            result['issues'].append(f"缺少必需字段: {missing_fields}")
        
        # 检查数据完整性
        for field in required_fields:
            if field in df.columns:
                empty_count = df[field].isna().sum() + (df[field] == '').sum()
                if empty_count > 0:
                    result['issues'].append(f"{field} 有 {empty_count} 个空值")
        
        # 检查价格数据
        if 'Regular price' in df.columns:
            price_issues = []
            prices = pd.to_numeric(df['Regular price'], errors='coerce')
            zero_prices = (prices == 0).sum()
            invalid_prices = prices.isna().sum()
            
            if zero_prices > 0:
                price_issues.append(f"{zero_prices} 个零价格")
            if invalid_prices > 0:
                price_issues.append(f"{invalid_prices} 个无效价格")
            
            if price_issues:
                result['issues'].append(f"价格问题: {', '.join(price_issues)}")
        
        # 检查促销价格
        if 'Sale price' in df.columns and 'Regular price' in df.columns:
            regular_prices = pd.to_numeric(df['Regular price'], errors='coerce')
            sale_prices = pd.to_numeric(df['Sale price'], errors='coerce')
            
            # 检查促销价格是否合理
            valid_mask = (regular_prices > 0) & (sale_prices > 0)
            if valid_mask.sum() > 0:
                discount_rates = sale_prices[valid_mask] / regular_prices[valid_mask]
                avg_discount = discount_rates.mean()
                result['sample_data']['avg_discount_rate'] = f"{avg_discount:.2f} ({avg_discount*100:.1f}%)"
                
                # 检查是否有不合理的折扣
                unreasonable_discounts = ((discount_rates < 0.1) | (discount_rates > 0.9)).sum()
                if unreasonable_discounts > 0:
                    result['issues'].append(f"{unreasonable_discounts} 个不合理的折扣率")
        
        # 检查SKU唯一性
        if 'SKU' in df.columns:
            duplicate_skus = df['SKU'].duplicated().sum()
            if duplicate_skus > 0:
                result['issues'].append(f"{duplicate_skus} 个重复的SKU")
        
        # 检查图片字段
        if 'Images' in df.columns:
            images_with_data = (df['Images'].notna() & (df['Images'] != '')).sum()
            result['sample_data']['products_with_images'] = f"{images_with_data}/{len(df)} ({images_with_data/len(df)*100:.1f}%)"
        
        # 检查分类数据
        if 'Categories' in df.columns:
            uncategorized = (df['Categories'] == 'Uncategorized').sum()
            result['sample_data']['uncategorized_products'] = f"{uncategorized}/{len(df)} ({uncategorized/len(df)*100:.1f}%)"
        
        # 获取样本数据
        if len(df) > 0:
            sample_row = df.iloc[0]
            result['sample_data']['first_product'] = {
                'Name': str(sample_row.get('Name', ''))[:50],
                'SKU': str(sample_row.get('SKU', '')),
                'Regular price': str(sample_row.get('Regular price', '')),
                'Sale price': str(sample_row.get('Sale price', '')),
                'Categories': str(sample_row.get('Categories', ''))[:50],
                'Images': str(sample_row.get('Images', ''))[:100],
                'Description': str(sample_row.get('Description', ''))[:100]
            }
        
        # 计算质量分数
        quality_score = 100
        quality_score -= len(result['issues']) * 10  # 每个问题扣10分
        quality_score = max(0, quality_score)
        result['quality_score'] = quality_score
        
        return result
        
    except Exception as e:
        return {
            'file_name': csv_file.name,
            'error': str(e),
            'quality_score': 0
        }

def main():
    """主验证函数"""
    output_dir = Path("woocommerce_output_de")
    
    if not output_dir.exists():
        print("❌ 输出目录不存在!")
        return
    
    csv_files = list(output_dir.glob("*.csv"))
    
    if not csv_files:
        print("❌ 没有找到CSV文件!")
        return
    
    print(f"📊 验证 {len(csv_files)} 个转换结果文件")
    print("="*80)
    
    total_products = 0
    total_quality_score = 0
    
    for i, csv_file in enumerate(csv_files, 1):
        print(f"\n{i}. 验证文件: {csv_file.name}")
        print("-" * 60)
        
        result = verify_woocommerce_csv(csv_file)
        
        if 'error' in result:
            print(f"❌ 验证失败: {result['error']}")
            continue
        
        print(f"产品数量: {result['total_products']:,}")
        print(f"文件大小: {result['file_size_mb']} MB")
        print(f"质量分数: {result['quality_score']}/100")
        
        total_products += result['total_products']
        total_quality_score += result['quality_score']
        
        # 显示问题
        if result['issues']:
            print("⚠️  发现的问题:")
            for issue in result['issues']:
                print(f"  - {issue}")
        else:
            print("✅ 无问题发现")
        
        # 显示样本数据
        if result['sample_data']:
            print("📋 数据统计:")
            for key, value in result['sample_data'].items():
                if key != 'first_product':
                    print(f"  {key}: {value}")
        
        # 显示第一个产品样本
        if 'first_product' in result['sample_data']:
            print("🔍 第一个产品样本:")
            for key, value in result['sample_data']['first_product'].items():
                print(f"  {key}: {value}")
    
    # 总结报告
    print("\n" + "="*80)
    print("📈 总结报告")
    print("="*80)
    
    print(f"总文件数: {len(csv_files)}")
    print(f"总产品数: {total_products:,}")
    print(f"平均质量分数: {total_quality_score/len(csv_files):.1f}/100")
    
    # 文件大小统计
    total_size = sum(f.stat().st_size for f in csv_files) / 1024 / 1024
    print(f"总文件大小: {total_size:.2f} MB")
    
    # 给出评估
    avg_quality = total_quality_score / len(csv_files)
    if avg_quality >= 90:
        print("🎉 转换质量: 优秀")
    elif avg_quality >= 80:
        print("✅ 转换质量: 良好")
    elif avg_quality >= 70:
        print("⚠️  转换质量: 一般")
    else:
        print("❌ 转换质量: 需要改进")

if __name__ == "__main__":
    main()
