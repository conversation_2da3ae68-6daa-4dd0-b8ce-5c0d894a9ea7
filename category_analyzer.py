#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析类目脚本 - 用于处理各种数据的分类分析
"""

import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter
import re

class CategoryAnalyzer:
    def __init__(self):
        self.category_patterns = {
            # 家具类
            'furniture': ['chair', 'table', 'desk', 'bed', 'sofa', 'cabinet', 'shelf', 'furniture'],
            # 工具类
            'tools': ['drill', 'hammer', 'saw', 'wrench', 'tool', 'equipment'],
            # 电子产品
            'electronics': ['tv', 'phone', 'computer', 'tablet', 'electronic', 'device'],
            # 服装类
            'clothing': ['shirt', 'pants', 'dress', 'jacket', 'clothing', 'apparel'],
            # 家居用品
            'home': ['kitchen', 'bathroom', 'living', 'home', 'house', 'decor'],
            # 运动用品
            'sports': ['sport', 'fitness', 'gym', 'exercise', 'outdoor', 'camping'],
            # 汽车用品
            'automotive': ['car', 'auto', 'vehicle', 'motor', 'automotive', 'parts'],
            # 宠物用品
            'pets': ['pet', 'dog', 'cat', 'animal', 'pet supplies'],
            # 美容健康
            'beauty': ['beauty', 'cosmetic', 'skincare', 'health', 'wellness'],
            # 玩具游戏
            'toys': ['toy', 'game', 'play', 'kids', 'children', 'baby']
        }

    def analyze_categories_from_csv(self, file_path: str):
        """分析CSV文件中的分类信息"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            print(f"📊 分析文件: {Path(file_path).name}")
            print(f"总行数: {len(df)}")
            print(f"总列数: {len(df.columns)}")
            
            # 查找可能的分类列
            category_columns = []
            for col in df.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['category', 'categories', 'type', 'class', 'group']):
                    category_columns.append(col)
            
            print(f"\n🏷️ 发现的分类列: {category_columns}")
            
            # 分析每个分类列
            for col in category_columns:
                self.analyze_single_category_column(df, col)
            
            # 如果没有明显的分类列，尝试从产品名称推断
            if not category_columns:
                name_columns = []
                for col in df.columns:
                    col_lower = str(col).lower()
                    if any(keyword in col_lower for keyword in ['name', 'title', 'product', 'item']):
                        name_columns.append(col)
                
                print(f"\n📝 从产品名称推断分类，发现名称列: {name_columns}")
                for col in name_columns[:1]:  # 只分析第一个名称列
                    self.infer_categories_from_names(df, col)
            
            return df
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None

    def analyze_single_category_column(self, df: pd.DataFrame, column: str):
        """分析单个分类列"""
        print(f"\n📂 分析分类列: {column}")
        
        categories = df[column].fillna('').astype(str)
        non_empty = categories[categories != ''].tolist()
        
        print(f"非空分类数: {len(non_empty)}/{len(df)} ({len(non_empty)/len(df)*100:.1f}%)")
        
        # 分类统计
        category_counts = Counter(non_empty)
        
        print(f"唯一分类数: {len(category_counts)}")
        print(f"前10分类:")
        for cat, count in category_counts.most_common(10):
            print(f"  • {cat}: {count} 个产品")
        
        # 分析分类层级
        hierarchical = [cat for cat in non_empty if '>' in cat or '/' in cat or '|' in cat]
        print(f"层级分类: {len(hierarchical)}/{len(non_empty)} ({len(hierarchical)/len(non_empty)*100:.1f}%)")
        
        if hierarchical:
            print(f"层级分类样本:")
            for cat in hierarchical[:5]:
                print(f"  • {cat}")

    def infer_categories_from_names(self, df: pd.DataFrame, name_column: str):
        """从产品名称推断分类"""
        print(f"\n🔍 从产品名称推断分类: {name_column}")
        
        names = df[name_column].fillna('').astype(str).tolist()
        inferred_categories = []
        
        for name in names:
            name_lower = name.lower()
            matched_category = 'Other'
            
            for category, keywords in self.category_patterns.items():
                if any(keyword in name_lower for keyword in keywords):
                    matched_category = category.title()
                    break
            
            inferred_categories.append(matched_category)
        
        # 统计推断结果
        category_counts = Counter(inferred_categories)
        
        print(f"推断分类统计:")
        for cat, count in category_counts.most_common():
            print(f"  • {cat}: {count} 个产品 ({count/len(names)*100:.1f}%)")
        
        # 添加推断分类到DataFrame
        df['Inferred_Category'] = inferred_categories
        
        return df

    def analyze_directory(self, directory_path: str):
        """分析目录中的所有CSV文件"""
        directory = Path(directory_path)
        
        if not directory.exists():
            print(f"❌ 目录不存在: {directory_path}")
            return
        
        csv_files = list(directory.glob("*.csv"))
        
        if not csv_files:
            print(f"❌ 目录中没有CSV文件: {directory_path}")
            return
        
        print(f"🔍 分析目录: {directory_path}")
        print(f"找到 {len(csv_files)} 个CSV文件")
        
        all_categories = []
        
        for i, file_path in enumerate(csv_files, 1):
            print(f"\n[{i}/{len(csv_files)}] " + "="*50)
            df = self.analyze_categories_from_csv(str(file_path))
            
            if df is not None and 'Inferred_Category' in df.columns:
                all_categories.extend(df['Inferred_Category'].tolist())
        
        # 生成总体分类报告
        if all_categories:
            print(f"\n" + "="*60)
            print(f"📈 总体分类分析报告")
            print("="*60)
            
            total_counts = Counter(all_categories)
            
            print(f"总产品数: {len(all_categories)}")
            print(f"分类分布:")
            for cat, count in total_counts.most_common():
                print(f"  • {cat}: {count:,} 个产品 ({count/len(all_categories)*100:.1f}%)")

    def export_category_analysis(self, input_file: str, output_file: str = None):
        """导出分类分析结果"""
        df = self.analyze_categories_from_csv(input_file)
        
        if df is not None and 'Inferred_Category' in df.columns:
            if output_file is None:
                input_path = Path(input_file)
                output_file = f"{input_path.stem}_category_analysis.csv"
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n✅ 分类分析结果已导出: {output_file}")
            
            return output_file
        
        return None

def main():
    """主函数 - 提供使用示例"""
    analyzer = CategoryAnalyzer()
    
    print("🏷️ 分类分析工具")
    print("="*50)
    
    # 使用示例
    print("📋 使用方法:")
    print("1. 分析单个CSV文件:")
    print("   analyzer.analyze_categories_from_csv('your_file.csv')")
    print()
    print("2. 分析整个目录:")
    print("   analyzer.analyze_directory('your_directory')")
    print()
    print("3. 导出分析结果:")
    print("   analyzer.export_category_analysis('input.csv', 'output.csv')")
    
    # 如果input目录存在，分析它
    if Path("input").exists():
        print(f"\n🚀 自动分析input目录...")
        analyzer.analyze_directory("input")

if __name__ == "__main__":
    main()
