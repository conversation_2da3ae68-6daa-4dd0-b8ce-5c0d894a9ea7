{"chunk_size": 10000, "max_workers": 4, "memory_limit_mb": 2048, "output_format": "csv", "field_mappings": {"post_title": "Name", "regular_price": "Regular price", "sku": "SKU", "stock": "Stock", "tax:product_cat": "Categories", "images": "Images", "attribute:Brand": "Brands", "attribute:MPN": "MPN", "attribute:UPC": "UPC"}, "data_validation": {"required_fields": ["Name", "Regular price", "Categories"], "price_min": 0, "price_max": 999999, "name_min_length": 3, "name_max_length": 255}, "output_options": {"include_attributes": true, "include_meta": true, "compress_output": false, "backup_original": true}, "logging": {"level": "INFO", "file_rotation": true, "max_file_size_mb": 10, "backup_count": 5}}