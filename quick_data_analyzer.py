#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据分析脚本 - 用于快速分析各种数据文件
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

def analyze_file(file_path: str, max_rows: int = 5):
    """快速分析单个文件"""
    try:
        file_path = Path(file_path)
        print(f"📊 分析文件: {file_path.name}")
        print("="*60)
        
        # 读取文件
        if file_path.suffix.lower() == '.csv':
            df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=1000)  # 只读前1000行进行快速分析
        elif file_path.suffix.lower() in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path, nrows=1000)
        else:
            print(f"❌ 不支持的文件格式: {file_path.suffix}")
            return None
        
        # 基本信息
        print(f"📈 基本信息:")
        print(f"  • 行数: {len(df):,}")
        print(f"  • 列数: {len(df.columns):,}")
        print(f"  • 文件大小: {file_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        # 列信息
        print(f"\n📋 列信息:")
        for i, col in enumerate(df.columns, 1):
            non_null = df[col].notna().sum()
            null_pct = (len(df) - non_null) / len(df) * 100
            dtype = str(df[col].dtype)
            
            print(f"  {i:2d}. {col[:40]:<40} | {dtype:<10} | 非空: {non_null:,} ({100-null_pct:.1f}%)")
        
        # 数据预览
        print(f"\n👀 数据预览 (前{max_rows}行):")
        print("-" * 80)
        
        # 只显示前几列，避免输出过宽
        display_cols = df.columns[:8] if len(df.columns) > 8 else df.columns
        preview_df = df[display_cols].head(max_rows)
        
        for idx, row in preview_df.iterrows():
            print(f"行 {idx + 1}:")
            for col in display_cols:
                value = str(row[col])[:50]  # 限制显示长度
                print(f"  {col}: {value}")
            print()
        
        # 数值列统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"🔢 数值列统计:")
            for col in numeric_cols[:5]:  # 只显示前5个数值列
                series = df[col].dropna()
                if len(series) > 0:
                    print(f"  {col}:")
                    print(f"    平均值: {series.mean():.2f}")
                    print(f"    中位数: {series.median():.2f}")
                    print(f"    最小值: {series.min():.2f}")
                    print(f"    最大值: {series.max():.2f}")
        
        # 文本列分析
        text_cols = df.select_dtypes(include=['object']).columns
        if len(text_cols) > 0:
            print(f"\n📝 文本列分析:")
            for col in text_cols[:5]:  # 只显示前5个文本列
                series = df[col].dropna().astype(str)
                if len(series) > 0:
                    unique_count = series.nunique()
                    avg_length = series.str.len().mean()
                    print(f"  {col}:")
                    print(f"    唯一值: {unique_count:,}")
                    print(f"    平均长度: {avg_length:.1f} 字符")
                    
                    # 显示最常见的值
                    if unique_count <= 20:
                        top_values = series.value_counts().head(3)
                        print(f"    常见值: {', '.join([f'{v}({c})' for v, c in top_values.items()])}")
        
        print("\n" + "="*60)
        return df
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_directory(directory_path: str):
    """分析目录中的所有数据文件"""
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"❌ 目录不存在: {directory_path}")
        return
    
    # 查找数据文件
    data_files = []
    for pattern in ['*.csv', '*.xlsx', '*.xls']:
        data_files.extend(directory.glob(pattern))
    
    if not data_files:
        print(f"❌ 目录中没有数据文件: {directory_path}")
        return
    
    print(f"🔍 批量分析目录: {directory_path}")
    print(f"找到 {len(data_files)} 个数据文件")
    print("\n" + "="*80)
    
    summary_data = []
    
    for i, file_path in enumerate(data_files, 1):
        print(f"\n[{i}/{len(data_files)}]")
        df = analyze_file(str(file_path), max_rows=2)  # 批量分析时只显示2行
        
        if df is not None:
            summary_data.append({
                'File': file_path.name,
                'Rows': len(df),
                'Columns': len(df.columns),
                'Size_MB': file_path.stat().st_size / 1024 / 1024,
                'Numeric_Cols': len(df.select_dtypes(include=[np.number]).columns),
                'Text_Cols': len(df.select_dtypes(include=['object']).columns)
            })
    
    # 生成汇总报告
    if summary_data:
        print(f"\n📊 汇总报告")
        print("="*80)
        
        summary_df = pd.DataFrame(summary_data)
        
        print(f"总文件数: {len(summary_df)}")
        print(f"总行数: {summary_df['Rows'].sum():,}")
        print(f"总大小: {summary_df['Size_MB'].sum():.2f} MB")
        print(f"平均列数: {summary_df['Columns'].mean():.1f}")
        
        print(f"\n📋 文件详情:")
        for _, row in summary_df.iterrows():
            print(f"  • {row['File']:<30} | {row['Rows']:>6,} 行 | {row['Columns']:>3} 列 | {row['Size_MB']:>6.2f} MB")

def main():
    """主函数"""
    print("🚀 快速数据分析工具")
    print("="*50)
    
    if len(sys.argv) > 1:
        # 命令行参数
        target = sys.argv[1]
        
        if Path(target).is_file():
            analyze_file(target)
        elif Path(target).is_dir():
            analyze_directory(target)
        else:
            print(f"❌ 路径不存在: {target}")
    else:
        # 交互模式
        print("📋 使用方法:")
        print("1. 分析单个文件: python quick_data_analyzer.py file.csv")
        print("2. 分析目录: python quick_data_analyzer.py directory/")
        print("3. 在代码中使用:")
        print("   analyze_file('your_file.csv')")
        print("   analyze_directory('your_directory')")
        
        # 自动分析当前目录的一些文件
        current_dir = Path(".")
        
        # 检查是否有input目录
        if (current_dir / "input").exists():
            print(f"\n💡 发现input目录，可以分析:")
            print(f"   analyze_directory('input')")
        
        # 检查是否有woocommerce文件
        wc_files = list(current_dir.glob("*woocommerce*.csv"))
        if wc_files:
            print(f"\n💡 发现WooCommerce文件:")
            for file in wc_files:
                print(f"   analyze_file('{file.name}')")
        
        # 检查是否有其他CSV文件
        csv_files = list(current_dir.glob("*.csv"))
        if csv_files:
            print(f"\n💡 发现CSV文件:")
            for file in csv_files[:5]:  # 只显示前5个
                print(f"   analyze_file('{file.name}')")

if __name__ == "__main__":
    main()
