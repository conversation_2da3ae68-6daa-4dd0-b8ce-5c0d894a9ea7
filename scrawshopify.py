import requests
import json
import csv
import re
from bs4 import BeautifulSoup

# ========== 配置 ==========
BASE_URL = "https://courtsidetennis.com"
OUTPUT_CSV = "woocommerce_products.csv"

# 固定库存 & 状态
DEFAULT_STOCK = 100
DEFAULT_STATUS = "publish"

# WooCommerce CSV 头
CSV_HEADERS = [
    "ID", "Type", "SKU", "Name", "Published", "Is featured?", "Visibility in catalog",
    "Short description", "Description", "Tax status", "In stock?", "Stock",
    "Regular price", "Sale price", "Categories", "Tags",
    "Images", "Attribute 1 name", "Attribute 1 value(s)", "Attribute 1 visible", "Attribute 1 global",
    "Attribute 2 name", "Attribute 2 value(s)", "Attribute 2 visible", "Attribute 2 global",
    "Parent"
]

# ========== 工具函数 ==========

def clean_html(raw_html: str) -> str:
    """清洗 Shopify 描述 HTML，移除无关内容"""
    soup = BeautifulSoup(raw_html, "html.parser")

    # 移除 script / style / noscript
    for tag in soup(["script", "style", "noscript"]):
        tag.decompose()

    # 移除带 shopify 或 ebay 的链接
    for a in soup.find_all("a", href=True):
        if "shopify" in a["href"] or "ebay" in a["href"]:
            a.decompose()

    # 移除站点域名图片（如果需要只保留产品图）
    for img in soup.find_all("img"):
        if "shopify" in img.get("src", ""):
            img.decompose()

    return str(soup)


def get_collections():
    """获取所有集合(分类)"""
    url = f"{BASE_URL}/collections.json"
    r = requests.get(url)
    if r.status_code != 200:
        return []
    data = r.json()
    collections = {c["id"]: c["title"] for c in data.get("collections", [])}
    return collections


def get_products_from_collection(collection_handle):
    """从集合获取产品列表"""
    products = []
    page = 1
    while True:
        url = f"{BASE_URL}/collections/{collection_handle}/products.json?page={page}"
        r = requests.get(url)
        if r.status_code != 200 or not r.json().get("products"):
            break
        products.extend(r.json()["products"])
        page += 1
    return products


def convert_to_woocommerce_rows(product, collection_name):
    """将 Shopify 产品转 WooCommerce CSV 格式"""
    rows = []
    product_id = product["id"]
    title = product["title"]
    body_html = clean_html(product.get("body_html", ""))
    short_desc = product.get("metafields_global_title_tag", "") or ""

    images = [img["src"] for img in product.get("images", [])]
    main_image = images[0] if images else ""
    gallery = ",".join(images[1:]) if len(images) > 1 else ""

    # 处理父产品
    parent_sku = f"shopify-{product_id}"
    parent_row = {
        "ID": "",
        "Type": "variable" if len(product.get("variants", [])) > 1 else "simple",
        "SKU": parent_sku,
        "Name": title,
        "Published": "1",
        "Is featured?": "0",
        "Visibility in catalog": "visible",
        "Short description": short_desc,
        "Description": body_html,
        "Tax status": "taxable",
        "In stock?": "1",
        "Stock": DEFAULT_STOCK,
        "Regular price": product["variants"][0].get("price", ""),
        "Sale price": "",
        "Categories": collection_name,
        "Tags": ",".join(product.get("tags", [])),
        "Images": main_image + ("," + gallery if gallery else ""),
        "Attribute 1 name": "Size",
        "Attribute 1 value(s)": ",".join(
            [v["option1"] for v in product["variants"] if v["option1"]]
        ),
        "Attribute 1 visible": "1",
        "Attribute 1 global": "1",
        "Attribute 2 name": "Color",
        "Attribute 2 value(s)": ",".join(
            [v["option2"] for v in product["variants"] if v["option2"]]
        ),
        "Attribute 2 visible": "1",
        "Attribute 2 global": "1",
        "Parent": "",
    }
    rows.append(parent_row)

    # 处理子变体
    for variant in product["variants"]:
        child_row = {
            "ID": "",
            "Type": "variation",
            "SKU": variant.get("sku") or f"{parent_sku}-{variant['id']}",
            "Name": title,
            "Published": "1",
            "Is featured?": "0",
            "Visibility in catalog": "visible",
            "Short description": "",
            "Description": "",
            "Tax status": "taxable",
            "In stock?": "1",
            "Stock": DEFAULT_STOCK,
            "Regular price": variant.get("price", ""),
            "Sale price": "",
            "Categories": "",
            "Tags": "",
            "Images": "",
            "Attribute 1 name": "Size",
            "Attribute 1 value(s)": variant.get("option1", ""),
            "Attribute 1 visible": "1",
            "Attribute 1 global": "1",
            "Attribute 2 name": "Color",
            "Attribute 2 value(s)": variant.get("option2", ""),
            "Attribute 2 visible": "1",
            "Attribute 2 global": "1",
            "Parent": parent_sku,
        }
        rows.append(child_row)

    return rows


# ========== 主程序 ==========

def main():
    collections = get_collections()
    all_rows = []

    for cid, cname in collections.items():
        print(f"采集分类: {cname}")
        products = get_products_from_collection(cname.lower().replace(" ", "-"))
        for product in products:
            rows = convert_to_woocommerce_rows(product, cname)
            all_rows.extend(rows)

    # 写入 CSV
    with open(OUTPUT_CSV, "w", newline="", encoding="utf-8-sig") as f:
        writer = csv.DictWriter(f, fieldnames=CSV_HEADERS)
        writer.writeheader()
        for row in all_rows:
            writer.writerow(row)

    print(f"✅ 已导出 {len(all_rows)} 条记录到 {OUTPUT_CSV}")


if __name__ == "__main__":
    main()
