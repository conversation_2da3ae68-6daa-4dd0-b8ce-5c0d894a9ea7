# 电商数据处理工具集

## 📋 项目简介

这是一个综合的电商数据处理工具集，包含SEO资产生成和WooCommerce数据转换两大核心功能。

## 🚀 主要功能

### 1. SEO资产生成 (`generate_seo_assets.py`)
- 🤖 AI驱动生成域名建议、Meta标题和Meta描述
- 🔄 双重结果生成，确保绝对差异化
- 📊 批量处理大量产品分类数据

### 2. WooCommerce数据转换 (`convert_to_woocommerce.py` / `advanced_converter.py`)
- 📦 将源Excel文件转换为WooCommerce CSV格式
- 🏷️ 智能处理分类、标签、图片等字段
- 💰 自动价格计算和SKU生成
- 🧹 高级HTML内容清理

### 3. 实用工具
- `simple_splitter.py` - CSV文件分割工具
- `analyze_categories_fixed.py` - 分类数据分析
- `check_domains.py` - 域名可用性检查

## 📦 安装

```bash
# 安装依赖
pip install -r requirements.txt

# 配置API密钥 (在generate_seo_assets.py中)
API_KEY = "your-deepseek-api-key-here"
```

## 🎯 快速使用

### SEO资产生成
```bash
# 生成SEO资产
python generate_seo_assets.py -i category_analysis_result.csv -o seo_results.csv -d 1
```

### WooCommerce数据转换
```bash
# 基础转换
python convert_to_woocommerce.py

# 高级转换 (推荐)
python advanced_converter.py
```

### 实用工具
```bash
# 分割CSV文件
python simple_splitter.py

# 分析分类数据
python analyze_categories_fixed.py

# 检查域名
python check_domains.py
```

## 📁 项目结构

```
├── 核心脚本/
│   ├── generate_seo_assets.py      # SEO资产生成
│   ├── convert_to_woocommerce.py   # 基础WooCommerce转换
│   └── advanced_converter.py      # 高级WooCommerce转换
├── 实用工具/
│   ├── simple_splitter.py         # CSV分割
│   ├── analyze_categories_fixed.py # 分类分析
│   └── check_domains.py           # 域名检查
├── 数据文件/
│   ├── 源数据文件/                # 输入Excel文件
│   ├── woocommerce_output/        # WooCommerce输出
│   └── output/                    # 其他输出
├── 配置文件/
│   ├── requirements.txt           # 依赖列表
│   └── converter_config.json     # 转换配置
└── 数据/
    ├── category_analysis_result.csv    # 分类分析结果
    ├── forced_differentiation_final.csv # SEO生成结果
    └── wc-simple.csv                   # WooCommerce模板
```

## 🔧 配置说明

### SEO生成配置
在 `generate_seo_assets.py` 中配置：
- `API_KEY`: DeepSeek API密钥
- `DELAY`: API调用延迟 (默认1秒)
- `LOG_LEVEL`: 日志级别

### WooCommerce转换配置
在 `converter_config.json` 中配置：
- 价格计算规则
- SKU生成规则
- HTML清理规则

## 📊 输出格式

### SEO资产输出
- `domain_suggestion`: 域名建议
- `meta_title`: Meta标题
- `meta_description`: Meta描述

### WooCommerce输出
- `SKU`: 产品SKU
- `Name`: 产品名称
- `Description`: 产品描述
- `Regular price`: 常规价格
- `Sale price`: 促销价格
- `Categories`: 产品分类
- `Tags`: 产品标签
- `Images`: 产品图片

## 🛠️ 高级功能

### 强制差异化引擎
- 基于创意种子的品牌原型选择
- 确保域名、标题、描述的绝对唯一性

### 智能数据处理
- 自动处理 `|||` 分隔符
- 智能HTML内容清理
- 价格计算和SKU生成

### 批量处理
- 支持大量数据批量处理
- 内存优化和错误处理
- 详细日志记录

## 🚨 常见问题

### API相关
- **API密钥错误**: 检查DeepSeek API密钥配置
- **网络超时**: 调整延迟时间或检查网络连接

### 数据处理
- **文件不存在**: 检查输入文件路径
- **格式错误**: 确保CSV/Excel文件格式正确

### WooCommerce转换
- **价格计算错误**: 检查价格字段格式
- **HTML清理问题**: 查看HTML处理日志

## 📈 性能优化

- 大批量数据建议分批处理
- 适当调整API延迟避免限流
- 使用DEBUG日志级别进行问题排查

## 📝 更新日志

### v2.0.0 (最新)
- ✅ 强制差异化SEO生成引擎
- ✅ 高级WooCommerce数据转换
- ✅ 智能HTML内容清理
- ✅ 完整的错误处理和日志系统

## 📞 支持

如有问题，请检查日志文件或提交Issue。