#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推荐域名生成脚本 - 基于产品数据生成相关域名建议
"""

import pandas as pd
import re
from collections import Counter
from pathlib import Path
import random

class DomainNameGenerator:
    def __init__(self):
        # 常用域名后缀
        self.tlds = ['.com', '.net', '.org', '.shop', '.store', '.online', '.co', '.io']
        
        # 电商相关词汇
        self.ecommerce_words = [
            'shop', 'store', 'mart', 'market', 'hub', 'depot', 'outlet', 'deals',
            'supply', 'gear', 'pro', 'plus', 'express', 'direct', 'world', 'zone',
            'center', 'place', 'house', 'warehouse', 'emporium', 'bazaar'
        ]
        
        # 品质词汇
        self.quality_words = [
            'best', 'top', 'premium', 'quality', 'elite', 'pro', 'expert', 'master',
            'super', 'mega', 'ultra', 'max', 'prime', 'select', 'choice', 'fine'
        ]
        
        # 行业特定词汇
        self.industry_keywords = {
            'furniture': ['furniture', 'home', 'living', 'decor', 'interior'],
            'tools': ['tools', 'hardware', 'equipment', 'workshop', 'craft'],
            'electronics': ['tech', 'digital', 'electronic', 'gadget', 'device'],
            'clothing': ['fashion', 'style', 'wear', 'apparel', 'clothing'],
            'automotive': ['auto', 'car', 'motor', 'vehicle', 'parts'],
            'sports': ['sports', 'fitness', 'outdoor', 'active', 'gear'],
            'beauty': ['beauty', 'cosmetic', 'wellness', 'care', 'spa'],
            'pets': ['pet', 'animal', 'companion', 'furry', 'paws']
        }

    def extract_keywords_from_data(self, file_path: str, max_keywords: int = 20):
        """从数据文件中提取关键词"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            # 查找产品名称列
            name_columns = []
            for col in df.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['name', 'title', 'product']):
                    name_columns.append(col)
            
            if not name_columns:
                print("❌ 未找到产品名称列")
                return []
            
            # 提取所有产品名称
            all_names = []
            for col in name_columns[:1]:  # 只使用第一个名称列
                names = df[col].fillna('').astype(str).tolist()
                all_names.extend(names)
            
            # 提取关键词
            keywords = []
            for name in all_names:
                # 清理和分词
                clean_name = re.sub(r'[^\w\s]', ' ', name.lower())
                words = clean_name.split()
                
                # 过滤有意义的词汇
                for word in words:
                    if len(word) > 3 and word.isalpha():
                        keywords.append(word)
            
            # 统计词频
            keyword_counts = Counter(keywords)
            
            # 返回最常见的关键词
            top_keywords = [word for word, count in keyword_counts.most_common(max_keywords)]
            
            print(f"📊 从 {Path(file_path).name} 提取的关键词:")
            for i, word in enumerate(top_keywords[:10], 1):
                count = keyword_counts[word]
                print(f"  {i:2d}. {word} ({count} 次)")
            
            return top_keywords
            
        except Exception as e:
            print(f"❌ 提取关键词失败: {e}")
            return []

    def generate_domain_names(self, keywords: list, industry: str = None, count: int = 50):
        """生成域名建议"""
        domains = []
        
        # 基于关键词的组合
        for keyword in keywords[:10]:  # 使用前10个关键词
            # 单词 + 电商词汇
            for ecom_word in self.ecommerce_words:
                domains.append(f"{keyword}{ecom_word}")
                domains.append(f"{ecom_word}{keyword}")
            
            # 品质词汇 + 关键词
            for quality_word in self.quality_words[:5]:
                domains.append(f"{quality_word}{keyword}")
                domains.append(f"{keyword}{quality_word}")
        
        # 行业特定组合
        if industry and industry in self.industry_keywords:
            industry_words = self.industry_keywords[industry]
            for ind_word in industry_words:
                for ecom_word in self.ecommerce_words[:5]:
                    domains.append(f"{ind_word}{ecom_word}")
                    domains.append(f"{ecom_word}{ind_word}")
        
        # 双关键词组合
        for i, keyword1 in enumerate(keywords[:5]):
            for keyword2 in keywords[i+1:6]:
                domains.append(f"{keyword1}{keyword2}")
                domains.append(f"{keyword2}{keyword1}")
                
                # 添加连接词
                domains.append(f"{keyword1}and{keyword2}")
                domains.append(f"{keyword1}plus{keyword2}")
        
        # 去重并随机选择
        unique_domains = list(set(domains))
        random.shuffle(unique_domains)
        
        return unique_domains[:count]

    def check_domain_availability_format(self, domain_names: list):
        """检查域名格式并生成完整域名"""
        formatted_domains = []
        
        for domain in domain_names:
            # 清理域名
            clean_domain = re.sub(r'[^a-zA-Z0-9]', '', domain.lower())
            
            # 长度检查
            if 5 <= len(clean_domain) <= 20:
                # 为每个域名生成多个TLD版本
                for tld in self.tlds:
                    formatted_domains.append(f"{clean_domain}{tld}")
        
        return formatted_domains

    def generate_domains_from_file(self, file_path: str, industry: str = None, output_file: str = None):
        """从文件生成域名建议"""
        print(f"🌐 为 {Path(file_path).name} 生成域名建议")
        
        # 提取关键词
        keywords = self.extract_keywords_from_data(file_path)
        
        if not keywords:
            print("❌ 无法提取关键词")
            return None
        
        # 生成域名
        domain_names = self.generate_domain_names(keywords, industry)
        
        # 格式化域名
        formatted_domains = self.check_domain_availability_format(domain_names)
        
        # 创建结果DataFrame
        results = []
        for domain in formatted_domains:
            base_name = domain.split('.')[0]
            tld = '.' + domain.split('.')[1]
            
            results.append({
                'Domain': domain,
                'Base_Name': base_name,
                'TLD': tld,
                'Length': len(base_name),
                'Industry': industry or 'General',
                'Type': self.classify_domain_type(base_name)
            })
        
        df_results = pd.DataFrame(results)
        
        # 按长度和类型排序
        df_results = df_results.sort_values(['Length', 'Type'])
        
        # 保存结果
        if output_file is None:
            input_name = Path(file_path).stem
            output_file = f"domain_suggestions_{input_name}.csv"
        
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 生成了 {len(formatted_domains)} 个域名建议")
        print(f"📁 保存到: {output_file}")
        
        # 显示前20个建议
        print(f"\n🏆 前20个域名建议:")
        for i, domain in enumerate(formatted_domains[:20], 1):
            print(f"  {i:2d}. {domain}")
        
        return output_file

    def classify_domain_type(self, domain_name: str):
        """分类域名类型"""
        if any(word in domain_name for word in self.quality_words):
            return 'Premium'
        elif any(word in domain_name for word in self.ecommerce_words):
            return 'Ecommerce'
        elif len(domain_name) <= 8:
            return 'Short'
        elif len(domain_name) <= 12:
            return 'Medium'
        else:
            return 'Long'

    def batch_generate_domains(self, directory_path: str):
        """批量为目录中的文件生成域名"""
        directory = Path(directory_path)
        
        if not directory.exists():
            print(f"❌ 目录不存在: {directory_path}")
            return
        
        csv_files = list(directory.glob("*.csv"))
        
        if not csv_files:
            print(f"❌ 目录中没有CSV文件: {directory_path}")
            return
        
        print(f"🚀 批量生成域名建议")
        print(f"目录: {directory_path}")
        print(f"文件数: {len(csv_files)}")
        
        generated_files = []
        
        for i, file_path in enumerate(csv_files, 1):
            print(f"\n[{i}/{len(csv_files)}] " + "="*50)
            
            # 尝试从文件名推断行业
            file_name = file_path.stem.lower()
            industry = None
            
            for ind, keywords in self.industry_keywords.items():
                if any(keyword in file_name for keyword in keywords):
                    industry = ind
                    break
            
            output_file = self.generate_domains_from_file(str(file_path), industry)
            if output_file:
                generated_files.append(output_file)
        
        print(f"\n🎉 批量生成完成!")
        print(f"生成的域名文件: {len(generated_files)}")
        for file in generated_files:
            print(f"  • {file}")

def main():
    """主函数"""
    generator = DomainNameGenerator()
    
    print("🌐 域名生成工具")
    print("="*50)
    
    print("📋 使用方法:")
    print("1. 为单个文件生成域名:")
    print("   generator.generate_domains_from_file('your_file.csv')")
    print()
    print("2. 批量生成域名:")
    print("   generator.batch_generate_domains('your_directory')")
    
    # 如果input目录存在，提供批量生成选项
    if Path("input").exists():
        print(f"\n💡 发现input目录，可以运行批量生成:")
        print(f"   generator.batch_generate_domains('input')")

if __name__ == "__main__":
    main()
