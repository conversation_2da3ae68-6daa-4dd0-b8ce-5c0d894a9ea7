#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEO资产生成工具 - 使用Gemini AI为电商分类数据生成SEO内容
优化版本：包含日志系统、重试机制、命令行参数、数据验证等功能
"""

import os
import csv
import json
import time
import logging
import argparse
import requests
from functools import wraps
from typing import Dict, Any, Optional

# 清除可能影响API调用的环境变量
def clear_proxy_settings():
    """清除代理设置以避免API调用问题"""
    proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY']
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"已清除环境变量: {var}")
    
    # 设置SSL验证环境变量
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    print("已设置SSL验证环境变量")

# 清除代理设置
clear_proxy_settings()

# 尝试导入openai，如果失败则提供安装指导
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    print("错误: 缺少 openai 模块")
    print("请运行以下命令安装:")
    print("pip install openai")
    print("或者:")
    print("python -m pip install openai")
    OPENAI_AVAILABLE = False

# --- 配置日志系统 ---
def setup_logging(log_level=logging.INFO):
    """配置日志系统"""
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('seo_generation.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# --- 重试装饰器 ---
def retry_with_backoff(max_retries=3, backoff_factor=2, exceptions=(Exception,)):
    """带指数退避的重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_retries - 1:
                        logger.error(f"重试 {max_retries} 次后仍然失败: {e}")
                        raise e
                    wait_time = backoff_factor ** attempt
                    logger.warning(f"第 {attempt + 1} 次尝试失败: {e}")
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
        return wrapper
    return decorator

# --- 配置 ---
if not OPENAI_AVAILABLE:
    logger.error("由于缺少必要的模块，脚本无法运行。")
    logger.error("请先安装所需的依赖包。")
    exit(1)

# API密钥（DeepSeek）
API_KEY = "***********************************" 

# 检查密钥是否被替换
if API_KEY == "***********************************" or not API_KEY:
    logger.warning("请将 'API_KEY' 替换为您真实的 DeepSeek API 密钥。")
    logger.warning("当前使用的是示例密钥，可能无法正常工作。")

try:
    # 测试DeepSeek API连接
    client = openai.OpenAI(
        api_key=API_KEY,
        base_url="https://api.deepseek.com/v1"
    )
    
    logger.info("DeepSeek API配置成功")
except Exception as e:
    logger.error(f"DeepSeek API配置失败: {e}")
    exit(1)

# --- 数据验证 ---
def validate_csv_data(row_data: Dict[str, Any]) -> bool:
    """验证CSV数据的完整性"""
    required_fields = ['文件名', '一级分类']
    missing_fields = [field for field in required_fields if not row_data.get(field, '').strip()]
    
    if missing_fields:
        logger.warning(f"行数据缺少必要字段: {missing_fields}")
        return False
    
    return True

def clean_text(text: str) -> str:
    """清理文本数据"""
    if not text:
        return 'N/A'
    return str(text).strip()

# --- 提示词构建 ---
def build_prompt(row_data: Dict[str, Any]) -> str:
    """根据CSV行数据构建一个详细的提示词"""
    primary_category = clean_text(row_data.get('一级分类', 'N/A'))
    sub_cat1 = clean_text(row_data.get('二级分类1', ''))
    sub_cat2 = clean_text(row_data.get('二级分类2', ''))
    filename = clean_text(row_data.get('文件名', 'N/A'))
    
    # 生成创意种子
    import time
    import random
    import hashlib
    
    # 基于行数据和当前时间生成唯一种子
    base_string = f"{filename}_{primary_category}_{sub_cat1}_{sub_cat2}_{time.time()}_{random.random()}"
    iteration_id = hashlib.md5(base_string.encode()).hexdigest()[:8]

    prompt = f"""
# ROLE
# 扮演一名顶级的品牌身份架构师与生成式SEO策略家。你的任务不是简单地填充模板，而是为每一个产品分类数据，从零开始构思并创造一个完整、自洽、且绝对独特的在线品牌身份。

# OBJECTIVE
# 根据输入的产品数据和每次指定的独特"创意种子"，生成一套包含域名、Meta Title和Meta Description的品牌资产。核心目标是实现"绝对差异化"：即使输入相同的产品数据，每次生成的输出也必须在风格、策略和用词上完全不同。

# IMPORTANT: 请严格按照JSON格式返回结果，不要包含任何其他解释或markdown格式。

# INPUT DATA
- 产品文件名: {filename}
    - 核心品类: {primary_category}
    - 重点子品类1: {sub_cat1}
    - 重点子品类2: {sub_cat2}
- **【关键】创意种子 (Iteration ID)**: {iteration_id}

# ==================================
# ===== 强制差异化执行引擎 =====
# ==================================

# 【第一步】基于创意种子强制选择品牌原型
# 使用创意种子的最后一位数字 (0-9) 强制选择品牌原型：
# 0-1: 智者 (The Sage) - 专业、权威、数据驱动
# 2-3: 英雄 (The Hero) - 强大、高效、性能至上  
# 4-5: 探险家 (The Explorer) - 自由、发现、探索未知
# 6-7: 创造者 (The Creator) - 创新、艺术、DIY
# 8-9: 魔术师 (The Magician) - 变革、神奇体验
# 如果最后一位是字母，则选择邻家普通人 (The Everyman) - 亲切、实用、高性价比

# 【第二步】基于创意种子强制选择组件组别
# 使用创意种子的倒数第二位数字强制选择：
# 域名后缀组别: 0-2=专业/商业型, 3-5=工艺/生活型, 6-7=性能/科技型, 8-9=宏大/高端型
# 标题动词组别: 0-2=直接行动, 3-5=发现探索, 6-7=赋能提升, 8-9=获取掌控
# 描述CTA组别: 0-2=紧迫感, 3-5=价值导向, 6-7=信任导向, 8-9=体验导向

# ==================================
# ===== 创意组件库 (Creative Palettes) =====
# ==================================

### 1. 品牌原型与风格 (Brand Archetype & Style)
- **智者 (The Sage):** 专业、权威、数据驱动、值得信赖。 (例如: TechLab, ProGear, ExpertHub)
- **英雄 (The Hero):** 强大、高效、战胜挑战、性能至上。 (例如: ToolEmpire, GearPrime, PowerVault)
- **探险家 (The Explorer):** 自由、发现、新奇、探索未知。 (例如: OutdoorUniverse, GearCraft, AdventureDen)
- **创造者 (The Creator):** 创新、艺术、DIY、打造梦想。 (例如: DesignStudio, CraftWorks, ArtisanNest)
- **魔术师 (The Magician):** 变革、神奇体验、化繁为简。 (例如: GadgetGalaxy, TechCosmos, MagicVault)
- **邻家普通人 (The Everyman):** 亲切、实用、可靠、高性价比。 (例如: HomeNest, ToolMart, FriendlyShop)

### 2. 域名组件 (Domain Components)
- **结构 (Structure):** [形容词/动词] + [产品词] + [后缀] 或 [产品词] + [后缀]
- **产品词根 (Keyword Root):** 从 {primary_category}, {sub_cat1}, {sub_cat2} 中提炼。
- **后缀 - 按风格分组 (Suffixes by Style):**
    - **专业/商业型:** Shop, Store, Hub, Pro, Elite, Center, Works, Lab
    - **工艺/生活型:** Craft, Nest, Space, Studio, Boutique, Den, Lounge
    - **性能/科技型:** Gear, Tech, Prime, Vault, Factory
    - **宏大/高端型:** Lux, Empire, Kingdom, World, Universe, Cosmos

### 3. Meta Title 组件 (Meta Title Components)
- **字符限制:** 严格控制在 50-60 字符。
- **核心结构:** [开头] + [产品与价值] | [品牌名/域名]
- **开头动词 - 按意图分组 (Verbs by Intent):**
    - **直接行动:** Buy, Shop, Get, Order, Grab, Pick
    - **发现探索:** Discover, Explore, Browse, Find, Uncover, Reveal
    - **赋能提升:** Upgrade, Enhance, Build, Create, Transform, Elevate
    - **获取掌控:** Secure, Unlock, Master, Access, Command, Control
- **价值主张 (Value Proposition):** 优质[子品类1], 专业[子品类2], 高效解决方案, 您的终极指南等。

### 4. Meta Description 组件 (Meta Description Components)
- **字符限制:** 严格控制在 130-160 字符。
- **核心结构:** [开场白] + [阐述价值与关键词] + [信任/独特卖点] + [CTA].
- **开场白 (Opening Hooks):**
    - **寻找方案:** 正在寻找完美的 {primary_category}？
    - **提升体验:** 用我们的 {sub_cat1} 和 {sub_cat2} 提升您的项目。
    - **直接介绍:** 探索我们精选的 {primary_category} 系列…
    - **彰显专业:** 作为 {primary_category} 领域的专家…
- **CTA (Call to Action) - 按策略分组:**
    - **紧迫感:**立即抢购, 优惠限时, 库存有限!
    - **价值导向:**发现超值优惠, 获取您的理想装备, 立即节省!
    - **信任导向:**放心购物, 品质保证, 探索专家之选。
    - **体验导向:**开启您的创造之旅, 立即探索, 轻松选购。

# ==================================
# ===== 强制差异化执行规则 =====
# ==================================

# 【规则1】域名绝对唯一性
- 每个域名必须使用不同的前缀词
- 每个域名必须使用不同组别的后缀
- 每个域名必须体现不同的品牌原型
- 绝对禁止任何形式的重复

# 【规则2】标题绝对差异化
- 每个标题必须使用不同组别的开头动词
- 每个标题必须使用不同的价值主张结构
- 每个标题必须体现不同的品牌语言风格
- 绝对禁止任何形式的重复

# 【规则3】描述绝对差异化
- 每个描述必须使用不同类型的开场白
- 每个描述必须使用不同组别的CTA策略
- 每个描述必须体现不同的品牌调性
- 绝对禁止任何形式的重复

# 【规则4】整体品牌差异化
- 每个站点必须体现完全不同的品牌原型
- 每个站点必须使用不同的目标用户群体定位
- 每个站点必须使用不同的价值主张
- 每个站点必须使用不同的服务特色

# EXECUTION RULES
1.  **强制差异化:** 基于创意种子 {iteration_id} 强制选择不同的品牌原型和组件组别
2.  **策略一致性:** 所选的"品牌原型"必须统一指导域名、标题和描述的风格、用词和CTA策略
3.  **SEO基础:** 必须自然地将 {primary_category}, {sub_cat1}, {sub_cat2} 融入标题和描述中
4.  **严格格式化:** 仅返回一个格式正确的JSON对象，不含任何解释
5.  **JSON格式要求:** 必须严格按照指定的JSON结构返回结果

# OUTPUT FORMAT
# Please return only a single, valid JSON object. Do not include any explanations or markdown.
# IMPORTANT: You must return a valid JSON format with the following structure:
    {{
        "domain_suggestion": "YourSuggestedDomain.com",
        "meta_title": "Your SEO Meta Title | Brand Name",
        "meta_description": "Your compelling SEO meta description with keywords and a CTA."
    }}
    """
    return prompt

# --- API调用 ---
@retry_with_backoff(max_retries=3, backoff_factor=2)
def generate_seo_content(row_data: Dict[str, Any], is_second_generation: bool = False) -> Dict[str, str]:
    """调用DeepSeek API生成SEO内容"""
    prompt = build_prompt(row_data)
    
    # 如果是第二份生成，添加额外的差异化要求
    if is_second_generation:
        # 为第二份生成创建完全不同的创意种子
        import time
        import random
        import hashlib
        
        filename = clean_text(row_data.get('文件名', 'N/A'))
        primary_category = clean_text(row_data.get('一级分类', 'N/A'))
        sub_cat1 = clean_text(row_data.get('二级分类1', ''))
        sub_cat2 = clean_text(row_data.get('二级分类2', ''))
        
        # 生成完全不同的创意种子
        second_base_string = f"SECOND_{filename}_{primary_category}_{sub_cat1}_{sub_cat2}_{time.time()}_{random.random()}"
        second_iteration_id = hashlib.md5(second_base_string.encode()).hexdigest()[:8]
        
        prompt += f"""

# ===== 第二份生成 - 强制差异化引擎 =====

# 重要：这是第二份生成，必须与第一份完全不同！
# IMPORTANT: 请严格按照JSON格式返回结果，不要包含任何其他解释或markdown格式。

# 新的创意种子 (Second Iteration ID): {second_iteration_id}

# 【强制差异化执行指令】

# 【第一步】基于新创意种子强制选择不同的品牌原型
# 使用新创意种子 {second_iteration_id} 的最后一位数字强制选择：
# 0-1: 智者 (The Sage) - 专业、权威、数据驱动
# 2-3: 英雄 (The Hero) - 强大、高效、性能至上  
# 4-5: 探险家 (The Explorer) - 自由、发现、探索未知
# 6-7: 创造者 (The Creator) - 创新、艺术、DIY
# 8-9: 魔术师 (The Magician) - 变革、神奇体验
# 如果最后一位是字母，则选择邻家普通人 (The Everyman) - 亲切、实用、高性价比

# 【第二步】基于新创意种子强制选择不同的组件组别
# 使用新创意种子 {second_iteration_id} 的倒数第二位数字强制选择：
# 域名后缀组别: 0-2=专业/商业型, 3-5=工艺/生活型, 6-7=性能/科技型, 8-9=宏大/高端型
# 标题动词组别: 0-2=直接行动, 3-5=发现探索, 6-7=赋能提升, 8-9=获取掌控
# 描述CTA组别: 0-2=紧迫感, 3-5=价值导向, 6-7=信任导向, 8-9=体验导向

# 【强制差异化要求】

# 1. 品牌原型绝对差异化：
#    - 必须选择与第一份完全不同的品牌原型
#    - 必须体现完全不同的品牌调性和语言风格
#    - 必须使用完全不同的目标用户群体定位

# 2. 域名绝对差异化：
#    - 必须使用与第一份完全不同的后缀组别
#    - 必须使用完全不同的域名前缀词
#    - 必须体现完全不同的品牌原型
#    - 绝对禁止任何形式的重复！

# 3. 标题绝对差异化：
#    - 必须使用与第一份不同组别的开头动词
#    - 必须使用完全不同的价值主张结构
#    - 必须体现完全不同的品牌语言风格
#    - 绝对禁止任何形式的重复！

# 4. 描述绝对差异化：
#    - 必须使用与第一份不同类型的开场白
#    - 必须使用与第一份不同组别的CTA策略
#    - 必须体现完全不同的品牌调性
#    - 绝对禁止任何形式的重复！

# 5. 整体品牌绝对差异化：
#    - 每个站点必须体现完全不同的品牌原型
#    - 每个站点必须使用不同的目标用户群体定位
#    - 每个站点必须使用不同的价值主张
#    - 每个站点必须使用不同的服务特色

# 记住：基于新的创意种子 {second_iteration_id}，强制创造完全不同的品牌身份和表达方式！"""
    
    try:
        client = openai.OpenAI(
            api_key=API_KEY,
            base_url="https://api.deepseek.com/v1"
        )
        
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "你是一个专业的SEO专家和品牌命名顾问，专门为电商产品生成SEO内容。每次生成都要力求独特性和创新性。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.9,
            max_tokens=1024,
            response_format={"type": "json_object"}
        )
        
        if not response.choices or not response.choices[0].message.content:
            raise ValueError("API返回空响应")
        
        response_text = response.choices[0].message.content
        
        try:
            result = json.loads(response_text)
            
            # 验证返回的JSON结构
            required_keys = ['domain_suggestion', 'meta_title', 'meta_description']
            if not all(key in result for key in required_keys):
                raise ValueError(f"API返回的JSON缺少必要字段: {required_keys}")
            
            # 验证字符长度
            if len(result['meta_title']) > 60:
                logger.warning(f"Meta Title 长度超过60字符: {len(result['meta_title'])}")
            
            if len(result['meta_description']) > 160:
                logger.warning(f"Meta Description 长度超过160字符: {len(result['meta_description'])}")
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.error(f"原始响应: {response_text}")
            raise ValueError(f"API返回无效JSON格式: {e}")
            
    except Exception as e:
        logger.error(f"DeepSeek API调用失败: {e}")
        raise e

# --- 文件处理 ---
def process_csv_file(input_file: str, output_file: str, delay: float = 2.0):
    """读取CSV文件，为每一行调用DeepSeek API生成两份不同结果，并将结果写入新的CSV文件"""
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        return False
    
    try:
        with open(input_file, mode='r', encoding='utf-8-sig') as infile, \
             open(output_file, mode='w', encoding='utf-8-sig', newline='') as outfile:
            
            reader = csv.DictReader(infile)
            
            if not reader.fieldnames:
                logger.error("CSV文件没有有效的列标题")
                return False
            
            # 修改输出头部，包含两份结果
            output_headers = list(reader.fieldnames) + [
                'domain_suggestion_1', 'meta_title_1', 'meta_description_1',
                'domain_suggestion_2', 'meta_title_2', 'meta_description_2',
                'processing_status'
            ]
            writer = csv.DictWriter(outfile, fieldnames=output_headers)
            writer.writeheader()

            logger.info(f"开始处理文件 '{input_file}'，每行生成两份不同结果，结果将保存到 '{output_file}'...")
            
            total_rows = 0
            success_count = 0
            error_count = 0

            for i, row in enumerate(reader):
                total_rows += 1
                filename = row.get('文件名', f'第{i+1}行')
                logger.info(f"--- 正在处理第 {i+1} 行: {filename} ---")
                
                # 数据验证
                if not validate_csv_data(row):
                    logger.warning(f"跳过无效数据行: {filename}")
                    row.update({
                        'domain_suggestion_1': 'INVALID_DATA',
                        'meta_title_1': 'INVALID_DATA',
                        'meta_description_1': 'INVALID_DATA',
                        'domain_suggestion_2': 'INVALID_DATA',
                        'meta_title_2': 'INVALID_DATA',
                        'meta_description_2': 'INVALID_DATA',
                        'processing_status': 'SKIPPED_INVALID_DATA'
                    })
                    error_count += 1
                    writer.writerow(row)
                    continue
                
                try:
                    # 生成第一份结果
                    logger.info(f"  生成第一份结果...")
                    result1 = generate_seo_content(row, is_second_generation=False)
                    
                    # 短暂延迟确保差异化
                    time.sleep(0.5)
                    
                    # 生成第二份结果 - 强制差异化
                    logger.info(f"  生成第二份结果...")
                    result2 = generate_seo_content(row, is_second_generation=True)
                    
                    # 更新行数据
                    row.update({
                        'domain_suggestion_1': result1.get('domain_suggestion', ''),
                        'meta_title_1': result1.get('meta_title', ''),
                        'meta_description_1': result1.get('meta_description', ''),
                        'domain_suggestion_2': result2.get('domain_suggestion', ''),
                        'meta_title_2': result2.get('meta_title', ''),
                        'meta_description_2': result2.get('meta_description', ''),
                        'processing_status': 'SUCCESS'
                    })
                    
                    success_count += 1
                    logger.info(f"  ✓ 处理成功! 域名1: {result1.get('domain_suggestion', 'N/A')}, 域名2: {result2.get('domain_suggestion', 'N/A')}")

                except Exception as e:
                    logger.error(f"  ✗ 处理该行时发生错误: {e}")
                    row.update({
                        'domain_suggestion_1': 'GENERATION_FAILED',
                        'meta_title_1': 'GENERATION_FAILED',
                        'meta_description_1': f'Error: {str(e)[:100]}...',
                        'domain_suggestion_2': 'GENERATION_FAILED',
                        'meta_title_2': 'GENERATION_FAILED',
                        'meta_description_2': f'Error: {str(e)[:100]}...',
                        'processing_status': 'FAILED'
                    })
                    error_count += 1
                
                writer.writerow(row)
                
                # 延迟以避免API限制
                if delay > 0:
                    time.sleep(delay)

            # 处理统计
            logger.info(f"\n=== 处理完成统计 ===")
            logger.info(f"总行数: {total_rows}")
            logger.info(f"成功处理: {success_count}")
            logger.info(f"失败/跳过: {error_count}")
            logger.info(f"成功率: {success_count/total_rows*100:.1f}%" if total_rows > 0 else "0%")
            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"每行生成了两份不同的SEO结果")
            
            return True

    except Exception as e:
        logger.error(f"处理文件时发生意外错误: {e}")
        return False

# --- 命令行参数解析 ---
def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='SEO资产生成工具 - 使用DeepSeek AI为电商分类数据生成SEO内容',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--input', '-i', 
        default='category_analysis_result.csv',
        help='输入CSV文件路径 (默认: category_analysis_result.csv)'
    )
    
    parser.add_argument(
        '--output', '-o', 
        default='seo_results_output.csv',
        help='输出CSV文件路径 (默认: seo_results_output.csv)'
    )
    
    parser.add_argument(
        '--delay', '-d', 
        type=float, 
        default=2.0,
        help='API调用间隔延迟(秒) (默认: 2.0)'
    )
    
    parser.add_argument(
        '--log-level', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='SEO Assets Generator v1.1.0'
    )
    
    return parser.parse_args()

# --- 主程序入口 ---
def main():
    """主程序入口"""
    args = parse_arguments()
    
    # 设置日志级别
    log_level = getattr(logging, args.log_level.upper())
    logger.setLevel(log_level)
    
    logger.info("=== SEO资产生成工具启动 ===")
    logger.info(f"输入文件: {args.input}")
    logger.info(f"输出文件: {args.output}")
    logger.info(f"API延迟: {args.delay}秒")
    logger.info(f"日志级别: {args.log_level}")
    
    # 检查输入文件
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        logger.info("请确保CSV文件存在并包含以下列: 文件名, 一级分类, 二级分类1, 二级分类2")
        return 1
    
    # 处理文件
    success = process_csv_file(args.input, args.output, args.delay)
    
    if success:
        logger.info("=== 程序执行完成 ===")
        return 0
    else:
        logger.error("=== 程序执行失败 ===")
        return 1

if __name__ == "__main__":
    exit(main())