#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试图片处理功能
"""

import re

def extract_image_urls_simple(images_str):
    """简化版图片URL提取"""
    urls = []
    
    # 直接使用正则表达式提取所有HTTP/HTTPS URL
    url_pattern = r'https?://[^\s<>"\'|,;]+\.(jpg|jpeg|png|gif|webp|bmp|svg)(?:\?[^\s<>"\'|,;]*)?'
    
    for match in re.finditer(url_pattern, images_str, re.IGNORECASE):
        url = match.group(0)
        # 清理URL末尾可能的引号
        url = re.sub(r'["\'>]+$', '', url)
        urls.append(url)
    
    # 如果没找到，尝试提取srcset中的URL
    if not urls:
        srcset_pattern = r'srcset\s*=\s*["\']([^"\']+)["\']'
        for match in re.finditer(srcset_pattern, images_str, re.IGNORECASE):
            srcset_value = match.group(1)
            for url in srcset_value.split(','):
                url = url.strip().split(' ')[0]
                if url.startswith('http') and any(ext in url.lower() for ext in ['.jpg', '.png', '.webp', '.gif']):
                    urls.append(url)
    
    return urls

def test_your_example():
    """测试你提供的具体例子"""
    test_input = 'https://media.cdn.bauhaus/m/1475267/12.webp" type="image/webp"><source srcset="https://media.cdn.bauhaus/m/1475267/12.jpg|https://media.cdn.bauhaus/m/1475267/12.jpg"|https://media.cdn.bauhaus/m/1474562/12.jpg"|https://media.cdn.bauhaus/m/1474286/12.jpg'
    
    print("测试输入:")
    print(test_input)
    print("\n" + "="*60)
    
    urls = extract_image_urls_simple(test_input)
    
    print(f"提取到 {len(urls)} 个URL:")
    for i, url in enumerate(urls, 1):
        print(f"{i}. {url}")
    
    # 去重
    unique_urls = list(dict.fromkeys(urls))  # 保持顺序的去重
    print(f"\n去重后 {len(unique_urls)} 个URL:")
    for i, url in enumerate(unique_urls, 1):
        print(f"{i}. {url}")

if __name__ == "__main__":
    test_your_example()
