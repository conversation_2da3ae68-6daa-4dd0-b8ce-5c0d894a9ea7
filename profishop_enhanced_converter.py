#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Profishop-DE 增强版转换器 - 基于深度分析的优化
"""

import pandas as pd
import re
import logging
import random
from pathlib import Path
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProfishopEnhancedConverter:
    def __init__(self):
        self.sku_counter = 1
        self.sku_prefix = "DE-PROF"
        self.used_skus = set()
        
        # 品牌标准化映射
        self.brand_standardization = {
            'bosch': 'Bosch',
            'makita': 'Makita',
            'dewalt': 'DeWalt',
            'milwaukee': 'Milwaukee',
            'festool': 'Festool',
            'hilti': 'Hilti',
            'metabo': 'Metabo',
            'ryobi': 'Ryobi'
        }
        
        # 专业工具分类映射
        self.category_mapping = {
            'drill': 'Tools > Power Tools > Drills',
            'saw': 'Tools > Power Tools > Saws',
            'grinder': 'Tools > Power Tools > Grinders',
            'hammer': 'Tools > Hand Tools > Hammers',
            'screwdriver': 'Tools > Hand Tools > Screwdrivers',
            'wrench': 'Tools > Hand Tools > Wrenches',
            'measuring': 'Tools > Measuring Tools',
            'safety': 'Safety > Personal Protection',
            'light': 'Tools > Lighting > Work Lights'
        }
        
        # 字段映射
        self.field_mapping = {
            'id': 'ID',
            'name': 'title',
            'description': 'alldes',
            'short_description': 'description s',
            'price': 'price',
            'sku_original': 'sku',
            'brand': 'Brand',
            'manufacturer': 'MFG',
            'category': 'category',
            'image_primary': 'image',
            'image_secondary': 'S-IMANGE',
            'tags': 'tags',
            'upc': 'UPC',
            'page_url': 'PageUrl',
            'basic_description': 'description'
        }

    def standardize_brand(self, brand: str) -> str:
        """标准化品牌名称"""
        if not brand or str(brand).strip() == 'nan':
            return ''
        
        brand_clean = str(brand).strip()
        brand_lower = brand_clean.lower()
        
        # 查找标准化映射
        for key, standard in self.brand_standardization.items():
            if key in brand_lower:
                return standard
        
        # 首字母大写
        return brand_clean.title()

    def extract_smart_description(self, alldes: str, basic_desc: str = "") -> tuple:
        """智能提取描述内容"""
        if not alldes or str(alldes).strip() == 'nan':
            return basic_desc, ""
        
        content = str(alldes)
        
        try:
            # 使用BeautifulSoup提取纯文本
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除脚本和样式
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 获取纯文本
            text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # 生成简短描述 (前150字符)
            short_desc = text[:150] + "..." if len(text) > 150 else text
            
            # 保留HTML格式的完整描述
            full_desc = self.clean_html_smart(content)
            
            return full_desc, short_desc
            
        except:
            # 如果BeautifulSoup失败，使用正则表达式
            text_only = re.sub(r'<[^>]+>', '', content)
            short_desc = text_only[:150] + "..." if len(text_only) > 150 else text_only
            full_desc = self.clean_html_smart(content)
            
            return full_desc, short_desc

    def clean_html_smart(self, html_content: str) -> str:
        """智能HTML清理 - 增强版"""
        if not html_content or str(html_content).strip() == 'nan':
            return ""
        
        content = str(html_content)
        
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除危险标签
            for tag in soup(['script', 'style', 'iframe', 'object', 'embed']):
                tag.decompose()
            
            # 智能标签替换
            for h1 in soup.find_all(['h1', 'h2']):
                h1.name = 'h3'
            
            for font in soup.find_all('font'):
                font.name = 'span'
            
            for center in soup.find_all('center'):
                center.name = 'div'
                center['style'] = 'text-align: center;'
            
            # 清理空标签
            for tag in soup.find_all():
                if not tag.get_text(strip=True) and not tag.find_all():
                    tag.decompose()
            
            # 移除事件属性
            for tag in soup.find_all():
                attrs_to_remove = [attr for attr in tag.attrs if attr.startswith('on')]
                for attr in attrs_to_remove:
                    del tag[attr]
            
            return str(soup)
            
        except:
            # 备用正则表达式清理
            replacements = {
                r'<h[12]([^>]*)>': r'<h3\1>',
                r'</h[12]>': '</h3>',
                r'<font([^>]*)>': r'<span\1>',
                r'</font>': '</span>',
                r'<center([^>]*)>': r'<div style="text-align: center;"\1>',
                r'</center>': '</div>'
            }
            
            for pattern, replacement in replacements.items():
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            
            return content.strip()

    def enhance_category(self, category: str, tags: str = "", brand: str = "") -> str:
        """增强分类处理"""
        if not category or str(category).strip() == 'nan':
            category = "Tools"
        
        category = str(category).strip()
        
        # 基于标签增强分类
        if tags and str(tags).strip() != 'nan':
            tags_lower = str(tags).lower()
            
            for keyword, enhanced_cat in self.category_mapping.items():
                if keyword in tags_lower:
                    return enhanced_cat
        
        # 基于品牌增强分类
        if brand and str(brand).strip() != 'nan':
            brand_lower = str(brand).lower()
            
            if any(tool_brand in brand_lower for tool_brand in ['bosch', 'makita', 'dewalt']):
                if 'tool' not in category.lower():
                    category = f"Tools > {category}"
        
        # 处理分类层级
        if '>' not in category and category.lower() != 'tools':
            category = f"Tools > {category}"
        
        return category

    def calculate_brand_aware_price(self, regular_price: float, brand: str = "") -> float:
        """基于品牌的智能定价"""
        if regular_price <= 0:
            return 0.0
        
        # 小于10的不打折
        if regular_price < 10:
            return regular_price
        
        # 基础折扣率
        if regular_price <= 50:
            base_discount = random.uniform(0.4, 0.7)
        elif regular_price <= 200:
            base_discount = random.uniform(0.6, 0.8)
        elif regular_price <= 500:
            base_discount = random.uniform(0.75, 0.9)
        else:
            base_discount = random.uniform(0.85, 0.95)
        
        # 品牌调整
        brand_lower = str(brand).lower() if brand else ""
        
        # 高端品牌折扣较少
        premium_brands = ['bosch', 'makita', 'dewalt', 'milwaukee', 'festool', 'hilti']
        if any(premium in brand_lower for premium in premium_brands):
            base_discount = min(base_discount + 0.1, 0.95)  # 减少折扣
        
        # 普通品牌可以更多折扣
        budget_brands = ['ryobi', 'black+decker', 'craftsman']
        if any(budget in brand_lower for budget in budget_brands):
            base_discount = max(base_discount - 0.1, 0.3)  # 增加折扣
        
        sale_price = regular_price * base_discount
        return round(sale_price, 2)

    def generate_enhanced_sku(self, row: pd.Series) -> str:
        """增强SKU生成"""
        
        # 策略1: 使用原始SKU
        original_sku = self.get_field_value(row, 'sku_original')
        if original_sku and len(original_sku) <= 15:
            sku = f"{self.sku_prefix}-{original_sku}"
            if sku not in self.used_skus:
                self.used_skus.add(sku)
                return sku
        
        # 策略2: 使用ID
        product_id = self.get_field_value(row, 'id')
        if product_id:
            sku = f"{self.sku_prefix}-{product_id}"
            if sku not in self.used_skus:
                self.used_skus.add(sku)
                return sku
        
        # 策略3: 品牌+产品特征
        brand = self.standardize_brand(self.get_field_value(row, 'brand'))
        name = self.get_field_value(row, 'name')
        tags = self.get_field_value(row, 'tags')
        
        components = [self.sku_prefix]
        
        # 品牌缩写
        if brand:
            brand_abbr = re.sub(r'[^\w]', '', brand)[:4].upper()
            components.append(brand_abbr)
        
        # 产品特征 (从名称和标签提取)
        keywords = []
        
        # 从产品名称提取
        if name:
            name_words = re.findall(r'\b\w{3,}\b', name.lower())
            keywords.extend(name_words[:2])
        
        # 从标签提取
        if tags and not keywords:
            tag_words = re.findall(r'\b\w{3,}\b', tags.lower())
            keywords.extend(tag_words[:2])
        
        if keywords:
            feature_part = ''.join([word[:3].upper() for word in keywords])
            components.append(feature_part[:6])
        
        # 序号
        components.append(f"{self.sku_counter:04d}")
        
        sku = '-'.join(components)
        
        # 确保唯一性和长度
        while sku in self.used_skus or len(sku) > 25:
            self.sku_counter += 1
            components[-1] = f"{self.sku_counter:04d}"
            sku = '-'.join(components)
            
            if len(sku) > 25:
                # 简化版本
                sku = f"{self.sku_prefix}-{self.sku_counter:06d}"
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku

    def get_field_value(self, row: pd.Series, field_key: str) -> str:
        """获取字段值"""
        field_name = self.field_mapping.get(field_key)
        if field_name and field_name in row.index and pd.notna(row[field_name]):
            return str(row[field_name]).strip()
        return ''

    def process_images_enhanced(self, row: pd.Series) -> str:
        """增强图片处理"""
        primary_image = self.get_field_value(row, 'image_primary')
        
        if primary_image and primary_image.strip():
            return primary_image.strip()
        
        # 使用备用图片
        secondary_image = self.get_field_value(row, 'image_secondary')
        
        if secondary_image and secondary_image.strip():
            logger.debug(f"使用备用图片: {secondary_image[:30]}...")
            return secondary_image.strip()
        
        return ""

    def convert_row_enhanced(self, row: pd.Series) -> Dict:
        """增强版行转换"""
        wc_row = {}
        
        # 基础信息
        wc_row['ID'] = ''
        wc_row['Type'] = 'simple'
        wc_row['SKU'] = self.generate_enhanced_sku(row)
        wc_row['Name'] = self.get_field_value(row, 'name')
        
        # 智能描述处理
        alldes = self.get_field_value(row, 'description')
        basic_desc = self.get_field_value(row, 'basic_description')
        
        full_desc, short_desc = self.extract_smart_description(alldes, basic_desc)
        
        wc_row['Description'] = full_desc
        wc_row['Short description'] = short_desc
        
        # 品牌标准化
        brand = self.standardize_brand(self.get_field_value(row, 'brand'))
        
        # 智能定价
        price_str = self.get_field_value(row, 'price')
        try:
            regular_price = float(price_str) if price_str else 0.0
        except:
            regular_price = 0.0
        
        sale_price = self.calculate_brand_aware_price(regular_price, brand)
        
        wc_row['Regular price'] = regular_price
        wc_row['Sale price'] = sale_price
        
        # 增强分类
        category = self.get_field_value(row, 'category')
        tags = self.get_field_value(row, 'tags')
        
        wc_row['Categories'] = self.enhance_category(category, tags, brand)
        wc_row['Tags'] = tags if tags else brand
        
        # 图片处理
        wc_row['Images'] = self.process_images_enhanced(row)
        
        # 外部链接
        page_url = self.get_field_value(row, 'page_url')
        wc_row['External URL'] = page_url
        wc_row['Button text'] = 'View on Profishop' if page_url else ''
        
        # 属性增强
        manufacturer = self.get_field_value(row, 'manufacturer')
        upc = self.get_field_value(row, 'upc')
        
        wc_row['Attribute 1 name'] = 'Brand'
        wc_row['Attribute 1 value(s)'] = brand
        wc_row['Attribute 1 visible'] = 1
        wc_row['Attribute 1 global'] = 0
        
        wc_row['Attribute 2 name'] = 'Manufacturer'
        wc_row['Attribute 2 value(s)'] = manufacturer
        wc_row['Attribute 2 visible'] = 1
        wc_row['Attribute 2 global'] = 0
        
        wc_row['Attribute 3 name'] = 'UPC'
        wc_row['Attribute 3 value(s)'] = upc
        wc_row['Attribute 3 visible'] = 0
        wc_row['Attribute 3 global'] = 0
        
        # 其他标准字段
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        wc_row['In stock?'] = 1
        wc_row['Stock'] = 999
        wc_row['Backorders allowed?'] = 0
        wc_row['Sold individually?'] = 0
        wc_row['Allow customer reviews?'] = 1
        
        return wc_row

    def process_profishop_enhanced(self, input_file: Path) -> bool:
        """增强版Profishop文件处理"""
        try:
            logger.info(f"🚀 开始增强处理: {input_file.name}")

            # 读取数据
            df = pd.read_excel(input_file)
            logger.info(f"📊 读取数据: {len(df)} 行, {len(df.columns)} 列")

            # 数据预处理统计
            stats = {
                'total_products': len(df),
                'brand_standardized': 0,
                'descriptions_enhanced': 0,
                'categories_enhanced': 0,
                'images_substituted': 0,
                'low_price_no_discount': 0,
                'external_links_added': 0
            }

            # 转换处理
            wc_rows = []

            for index, row in df.iterrows():
                if index % 1000 == 0 and index > 0:
                    logger.info(f"⏳ 已处理 {index}/{len(df)} 行...")

                wc_row = self.convert_row_enhanced(row)
                wc_rows.append(wc_row)

                # 统计增强功能使用情况
                original_brand = self.get_field_value(row, 'brand')
                if original_brand and wc_row['Attribute 1 value(s)'] != original_brand:
                    stats['brand_standardized'] += 1

                if wc_row['Short description'] and len(wc_row['Short description']) > 10:
                    stats['descriptions_enhanced'] += 1

                original_category = self.get_field_value(row, 'category')
                if '>' in wc_row['Categories'] and '>' not in str(original_category):
                    stats['categories_enhanced'] += 1

                if not self.get_field_value(row, 'image_primary') and wc_row['Images']:
                    stats['images_substituted'] += 1

                price = float(wc_row['Regular price']) if wc_row['Regular price'] else 0
                if 0 < price < 10 and wc_row['Sale price'] == wc_row['Regular price']:
                    stats['low_price_no_discount'] += 1

                if wc_row['External URL']:
                    stats['external_links_added'] += 1

            # 创建DataFrame并去重
            wc_df = pd.DataFrame(wc_rows)

            # 去重处理
            original_count = len(wc_df)
            wc_df = wc_df.drop_duplicates(subset=['SKU'], keep='first')
            duplicates_removed = original_count - len(wc_df)

            if duplicates_removed > 0:
                logger.info(f"🔄 去重: 移除 {duplicates_removed} 个重复SKU")

            # 保存结果
            output_dir = Path("woocommerce_output_profishop_enhanced")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / f"{input_file.stem}_enhanced.csv"

            # 确保所有WooCommerce列都存在
            wc_columns = [
                'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
                'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
                'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
                'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
                'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
                'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
                'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
                'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
                'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
                'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global'
            ]

            # 添加缺失的列
            for col in wc_columns:
                if col not in wc_df.columns:
                    wc_df[col] = ''

            # 按标准列顺序输出
            wc_df = wc_df[wc_columns]

            # 保存CSV
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            # 详细报告
            logger.info(f"✅ 转换完成: {output_file}")
            logger.info(f"📊 处理统计:")
            logger.info(f"  • 总产品数: {stats['total_products']:,}")
            logger.info(f"  • 最终输出: {len(wc_df):,}")
            logger.info(f"  • 品牌标准化: {stats['brand_standardized']}")
            logger.info(f"  • 描述增强: {stats['descriptions_enhanced']}")
            logger.info(f"  • 分类增强: {stats['categories_enhanced']}")
            logger.info(f"  • 图片替补: {stats['images_substituted']}")
            logger.info(f"  • 低价无折扣: {stats['low_price_no_discount']}")
            logger.info(f"  • 外部链接: {stats['external_links_added']}")

            return True

        except Exception as e:
            logger.error(f"❌ 处理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

def main():
    """主函数"""
    converter = ProfishopEnhancedConverter()

    source_dir = Path("源数据文件/de")

    if not source_dir.exists():
        logger.error("❌ 德国数据目录不存在!")
        return

    profishop_files = [f for f in source_dir.glob("profishop-de*.xlsx") if not f.name.startswith('~')]

    if not profishop_files:
        logger.error("❌ 没有找到profishop-de文件!")
        return

    logger.info(f"🚀 Profishop-DE 增强版转换器")
    logger.info(f"📂 数据目录: {source_dir}")
    logger.info(f"📊 找到 {len(profishop_files)} 个文件")

    logger.info(f"✨ 增强特性:")
    logger.info(f"  🏷️  品牌名称标准化")
    logger.info(f"  📝 智能描述提取和HTML清理")
    logger.info(f"  📂 多层分类构建")
    logger.info(f"  💰 基于品牌的智能定价")
    logger.info(f"  🖼️  图片智能替补")
    logger.info(f"  🔗 外部链接集成")
    logger.info(f"  🏷️  增强SKU生成")

    success_count = 0
    total_products = 0

    for i, file in enumerate(profishop_files, 1):
        logger.info(f"\n[{i}/{len(profishop_files)}] 处理文件: {file.name}")

        if converter.process_profishop_enhanced(file):
            success_count += 1

            # 统计产品数
            try:
                output_file = Path("woocommerce_output_profishop_enhanced") / f"{file.stem}_enhanced.csv"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8-sig') as f:
                        line_count = sum(1 for _ in f) - 1
                    total_products += line_count
            except:
                pass

    # 最终报告
    logger.info(f"\n" + "="*70)
    logger.info(f"🎉 Profishop-DE 增强版转换完成!")
    logger.info(f"="*70)
    logger.info(f"✅ 成功处理: {success_count}/{len(profishop_files)} 个文件")
    logger.info(f"📊 总产品数: {total_products:,} 个")
    logger.info(f"📁 输出目录: woocommerce_output_profishop_enhanced/")

    if success_count == len(profishop_files):
        logger.info(f"🎯 所有文件转换成功!")
        logger.info(f"💡 增强功能已全部应用:")
        logger.info(f"  • 品牌标准化和智能定价")
        logger.info(f"  • 智能描述提取和分类增强")
        logger.info(f"  • 图片替补和外部链接")
        logger.info(f"  • 高质量SKU生成")
    else:
        logger.warning(f"⚠️  部分文件处理失败，请检查日志")

if __name__ == "__main__":
    main()
