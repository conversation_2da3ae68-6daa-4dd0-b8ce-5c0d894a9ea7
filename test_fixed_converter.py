#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的德国源数据转换脚本
"""

from de_source_converter import DESourceConverter
import pandas as pd
from pathlib import Path

def test_price_calculation():
    """测试价格计算功能"""
    print("🧪 测试价格计算功能")
    print("="*50)
    
    converter = DESourceConverter()
    
    # 测试用例
    test_cases = [
        (100.0, "100元价格"),
        (50.0, "50元价格"),
        (200.0, "200元价格"),
        (1000.0, "1000元价格"),
        (12999/100, "price/100格式"),  # 模拟bauhaus格式
    ]
    
    for original_price, description in test_cases:
        regular_price = converter.calculate_regular_price(original_price)
        sale_price = converter.calculate_sale_price(regular_price)
        
        print(f"{description}:")
        print(f"  原价: {original_price}")
        print(f"  常规价: {regular_price}")
        print(f"  促销价: {sale_price}")
        print(f"  折扣率: {sale_price/regular_price*100:.1f}%")
        print()

def test_html_cleaning():
    """测试HTML清理功能"""
    print("🧪 测试HTML清理功能")
    print("="*50)
    
    converter = DESourceConverter()
    
    test_cases = [
        "<h1>产品标题</h1><p>产品描述</p>",
        "<font color='red'>红色文字</font><center>居中文本</center>",
        "<script>alert('危险')</script><p>安全内容</p>",
        "<h2>副标题</h2><div>普通文本</div>",
    ]
    
    for html in test_cases:
        cleaned = converter.clean_html(html)
        print(f"原始: {html}")
        print(f"清理后: {cleaned}")
        print()

def test_field_mapping():
    """测试字段映射功能"""
    print("🧪 测试字段映射功能")
    print("="*50)
    
    converter = DESourceConverter()
    
    # 模拟不同格式的数据行
    test_rows = [
        {
            'name': 'bauhaus格式',
            'data': {
                'ID': 1,
                'title': 'Test Product Bauhaus',
                'Brand': 'Test Brand',
                'price/100': 12999,
                'detail': '<h2>产品详情</h2><p>这是产品描述</p>',
                'cate': 'Electronics>Computers>Laptops',
                'image0': 'https://example.com/image1.jpg',
                'mpn': 'TEST-MPN-123'
            },
            'format': 'bauhaus_format'
        },
        {
            'name': 'bloomled格式',
            'data': {
                'ID': 2,
                'title': 'Test Product Bloomled',
                'Brand': 'Another Brand',
                'price': 99.99,
                'description': '<p>完整描述内容</p>',
                'description s': '短描述内容',
                'category': 'Home>Garden>Tools',
                'image': 'https://example.com/img1.jpg|https://example.com/img2.jpg',
                'UPC': '123456789'
            },
            'format': 'bloomled_format'
        }
    ]
    
    for test_row in test_rows:
        print(f"测试 {test_row['name']}:")
        wc_row = converter.convert_row_to_wc(test_row['data'], test_row['format'])
        
        print(f"  Name: {wc_row['Name']}")
        print(f"  SKU: {wc_row['SKU']}")
        print(f"  Regular price: {wc_row['Regular price']}")
        print(f"  Sale price: {wc_row['Sale price']}")
        print(f"  Categories: {wc_row['Categories']}")
        print(f"  Images: {wc_row['Images']}")
        print(f"  Description: {wc_row['Description'][:100]}...")
        print(f"  Brands: {wc_row['Brands']}")
        print()

def test_file_conversion():
    """测试实际文件转换"""
    print("🧪 测试实际文件转换")
    print("="*50)
    
    converter = DESourceConverter()
    
    # 测试第一个文件
    input_file = Path("源数据文件/de/bauhaus-at-de-图片前两图.xlsx")
    output_dir = Path("test_output_fixed")
    output_dir.mkdir(exist_ok=True)
    
    if input_file.exists():
        print(f"转换文件: {input_file.name}")
        success = converter.process_file(input_file, output_dir)
        
        if success:
            output_file = output_dir / f"{input_file.stem}_woocommerce.csv"
            if output_file.exists():
                df = pd.read_csv(output_file)
                print(f"✅ 转换成功！")
                print(f"输出行数: {len(df)}")
                
                # 检查关键字段
                sample_row = df.iloc[0]
                print(f"\n第一行数据检查:")
                print(f"Name: {sample_row['Name']}")
                print(f"Regular price: {sample_row['Regular price']}")
                print(f"Sale price: {sample_row['Sale price']}")
                print(f"Categories: {sample_row['Categories']}")
                print(f"Description: {str(sample_row['Description'])[:100]}...")
                
                # 验证价格计算
                if sample_row['Sale price'] > 0 and sample_row['Regular price'] > 0:
                    discount_rate = sample_row['Sale price'] / sample_row['Regular price']
                    print(f"折扣率: {discount_rate*100:.1f}%")
                    
                    if 0.1 <= discount_rate <= 0.6:
                        print("✅ 价格计算正确")
                    else:
                        print("❌ 价格计算可能有问题")
                
                return True
            else:
                print("❌ 输出文件未生成")
        else:
            print("❌ 转换失败")
    else:
        print(f"❌ 输入文件不存在: {input_file}")
    
    return False

if __name__ == "__main__":
    print("🔧 测试修复后的德国源数据转换脚本")
    print("="*80)
    
    # 测试价格计算
    test_price_calculation()
    
    print("\n" + "="*80)
    
    # 测试HTML清理
    test_html_cleaning()
    
    print("\n" + "="*80)
    
    # 测试字段映射
    test_field_mapping()
    
    print("\n" + "="*80)
    
    # 测试文件转换
    test_file_conversion()
    
    print("\n🎉 所有测试完成！")
