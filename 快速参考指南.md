# 德国电商数据转换系统 - 快速参考指南

## 🚀 快速开始

### 1. 运行转换脚本
```bash
# 转换所有德国源文件
python de_source_converter.py

# 检查转换结果
python verify_conversion_results.py

# 检查HTML处理情况
python simple_html_check.py
```

### 2. 文件结构
```
项目根目录/
├── de_source_converter.py      # 主转换脚本
├── verify_conversion_results.py # 结果验证脚本
├── simple_html_check.py        # HTML检查脚本
├── 详细需求文档.md              # 完整需求文档
├── 系统架构图.md               # 技术架构说明
├── 快速参考指南.md             # 本文件
├── 源文件/                     # Excel源文件目录
│   ├── bauhaus-at-de-图片前两图.xlsx
│   ├── beliani-de-3-or-en.xlsx
│   ├── bloomled-de.xlsx
│   └── ... (其他16个文件)
└── woocommerce_output_de/      # 输出目录
    ├── bauhaus-at-de-图片前两图_woocommerce.csv
    ├── beliani-de-3-or-en_woocommerce.csv
    └── ... (对应的输出文件)
```

## 📋 支持的数据格式

### WordPress格式
```
识别特征: Name, Regular price, Sale price, Description, Categories, Images, SKU
适用文件: bauhaus-at-de-图片前两图.xlsx
```

### Bauhaus格式  
```
识别特征: 产品名称, 常规价格, 促销价格, 产品描述, 产品分类, 产品图片, 产品编号
适用文件: beliani-de-3-or-en.xlsx
```

### Bloomled格式
```
识别特征: 标题, 价格, 折扣价, 详细描述, 类别, 图片链接, 商品编码
适用文件: bloomled-de.xlsx, klickparts-de.xlsx, lampenwelt-de.xlsx, 等
```

## 🔧 核心功能速查

### 价格计算策略
```python
价格区间 → 折扣范围
€0-50    → 30-60%折扣 (0.4-0.7倍)
€50-200  → 20-40%折扣 (0.6-0.8倍)  
€200-500 → 10-25%折扣 (0.75-0.9倍)
€500+    → 5-15%折扣 (0.85-0.95倍)
```

### HTML标签处理
```python
智能替换:
<h1>, <h2> → <h3>
<font> → <span>
<center> → <div style="text-align: center;">

保留标签:
<p>, <br>, <strong>, <em>, <ul>, <li>, <a>

移除标签:
<script>, <style>, <iframe>, onclick, onload
```

### 字段映射
```python
WooCommerce必需字段:
- SKU: 产品编号
- Name: 产品名称  
- Regular price: 常规价格
- Sale price: 促销价格
- Description: 详细描述
- Short description: 简短描述
- Categories: 产品分类
- Images: 产品图片
- Published: 1 (发布)
- In stock?: 1 (有库存)
- Stock: 999 (库存数量)
```

## 📊 处理状态监控

### 实时进度查看
```bash
# 查看处理日志
tail -f conversion.log

# 检查输出目录
ls -la woocommerce_output_de/

# 统计处理进度
find woocommerce_output_de/ -name "*.csv" | wc -l
```

### 常见状态信息
```
✅ 正常处理: "已处理 X 行..."
✅ 文件完成: "✓ filename.xlsx 处理成功"
⚠️  格式检测: "检测到文件格式: xxx_format"
❌ 处理错误: "处理文件时出错: ..."
```

## 🔍 质量检查清单

### 数据完整性检查
- [ ] 所有19个文件都有对应的CSV输出
- [ ] 产品数量统计正确 (~25万个产品)
- [ ] 必需字段无空值 (SKU, Name, Regular price)
- [ ] 价格数据合理 (Sale price ≤ Regular price)

### 格式标准检查  
- [ ] CSV文件编码为UTF-8-BOM
- [ ] 列顺序符合WooCommerce标准
- [ ] HTML内容安全无危险标签
- [ ] 图片链接格式正确

### 业务逻辑检查
- [ ] 价格折扣在合理范围内
- [ ] 产品分类映射正确
- [ ] 重复产品已去除
- [ ] 文件合并逻辑正确

## 🚨 常见问题解决

### 文件读取错误
```python
问题: "文件无法读取"
解决: 检查文件是否被其他程序占用，关闭Excel后重试
```

### 内存不足
```python
问题: "MemoryError"
解决: 减少BATCH_SIZE，或分批处理大文件
```

### 编码问题
```python
问题: "中文显示乱码"
解决: 确保使用UTF-8-BOM编码保存CSV
```

### 格式检测失败
```python
问题: "无法检测文件格式"
解决: 检查Excel文件列名，手动指定格式类型
```

## 📈 性能优化建议

### 处理速度优化
```python
# 调整批处理大小
BATCH_SIZE = 1000  # 默认值，可根据内存调整

# 启用并行处理
MAX_WORKERS = 4    # 根据CPU核心数调整

# 减少日志输出
LOG_LEVEL = 'INFO'  # 改为'WARNING'减少日志
```

### 内存使用优化
```python
# 分块读取大文件
chunk_size = 1000
for chunk in pd.read_excel(file, chunksize=chunk_size):
    process_chunk(chunk)

# 及时释放内存
del large_dataframe
gc.collect()
```

## 🔄 扩展和定制

### 添加新数据格式
```python
# 在detect_format()函数中添加新格式检测
def detect_new_format(df):
    if '新字段名' in df.columns:
        return 'new_format'
    return None

# 添加对应的字段映射
NEW_FORMAT_MAPPING = {
    '新字段名': 'Name',
    '新价格字段': 'Regular price',
    # ... 其他映射
}
```

### 自定义价格策略
```python
# 修改calculate_sale_price()函数
def custom_price_strategy(regular_price, category):
    if '特殊类别' in category:
        return regular_price * 0.8  # 20%折扣
    return calculate_sale_price(regular_price)
```

### 添加新的HTML处理规则
```python
# 在clean_html_content()中添加新规则
additional_replacements = {
    r'<新标签>': '<替换标签>',
    r'</新标签>': '</替换标签>'
}
```

## 📞 技术支持

### 日志文件位置
```
conversion.log          # 主处理日志
error.log              # 错误日志  
performance.log        # 性能日志
```

### 调试模式
```python
# 启用详细日志
logging.basicConfig(level=logging.DEBUG)

# 处理单个文件进行测试
process_single_file('test_file.xlsx')

# 验证特定功能
test_html_processing()
test_price_calculation()
```

### 联系信息
```
技术文档: 详细需求文档.md
架构说明: 系统架构图.md
问题反馈: 通过日志文件分析问题
```

---

**快速参考版本**: v1.0  
**最后更新**: 2025-09-01  
**适用版本**: de_source_converter.py v1.0
