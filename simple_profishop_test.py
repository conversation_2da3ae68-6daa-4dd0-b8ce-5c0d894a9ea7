#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.getcwd())

try:
    from profishop_de_converter import ProfishopDEConverter
    
    print("✅ 成功导入ProfishopDEConverter")
    
    # 创建转换器实例
    converter = ProfishopDEConverter()
    print("✅ 成功创建转换器实例")
    
    # 测试字段映射
    print(f"📋 字段映射: {len(converter.field_mapping)} 个字段")
    for key, value in converter.field_mapping.items():
        print(f"  {key} → {value}")
    
    # 创建简单测试数据
    test_row = pd.Series({
        'ID': 123,
        'title': 'Test Product',
        'price': 25.99,
        'alldes': '<h1>Test Description</h1>',
        'image': 'http://example.com/image.jpg',
        'S-IMANGE': 'http://example.com/backup.jpg'
    })
    
    print(f"\n🧪 测试基本功能:")
    
    # 测试字段获取
    name = converter.get_field_value(test_row, 'name')
    print(f"  产品名称: {name}")
    
    # 测试SKU生成
    sku = converter.generate_profishop_sku(test_row)
    print(f"  生成SKU: {sku}")
    
    # 测试价格计算
    sale_price = converter.calculate_sale_price(25.99)
    print(f"  促销价格: 25.99 → {sale_price}")
    
    # 测试小于10的价格
    low_sale_price = converter.calculate_sale_price(8.50)
    print(f"  低价格: 8.50 → {low_sale_price} (应该无折扣)")
    
    # 测试图片处理
    images = converter.process_images(test_row)
    print(f"  图片处理: {images}")
    
    print(f"\n✅ 基本功能测试通过!")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
