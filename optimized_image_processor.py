#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的图片处理器 - 专门处理复杂的图片字段格式
"""

import re
from typing import List, Set
from urllib.parse import urlparse, parse_qs

class OptimizedImageProcessor:
    def __init__(self, max_images: int = 5):
        self.max_images = max_images
    
    def process_complex_images(self, images_str: str) -> str:
        """处理复杂的图片字符串，支持你的具体格式"""
        if not images_str or str(images_str).strip() in ['', 'nan', 'null', 'None']:
            return ''
        
        # 提取所有URL
        urls = self.extract_all_urls(str(images_str))
        
        # 去重和验证
        unique_urls = self.deduplicate_urls(urls)
        
        # 限制数量
        final_urls = unique_urls[:self.max_images]
        
        return ','.join(final_urls) if final_urls else ''
    
    def extract_all_urls(self, text: str) -> List[str]:
        """从文本中提取所有可能的图片URL"""
        urls = []
        
        # 方法1: 直接正则匹配HTTP/HTTPS URL
        # 匹配以http开头，以图片扩展名结尾的URL
        url_pattern = r'https?://[^\s<>"\'|]+\.(?:jpg|jpeg|png|gif|webp|bmp|svg)(?:\?[^\s<>"\'|]*)?'
        
        for match in re.finditer(url_pattern, text, re.IGNORECASE):
            url = match.group(0)
            # 清理URL末尾可能的引号或特殊字符
            url = re.sub(r'["\'>|]+$', '', url)
            if self.is_valid_url(url):
                urls.append(url)
        
        # 方法2: 处理srcset属性
        srcset_matches = re.findall(r'srcset\s*=\s*["\']([^"\']+)["\']', text, re.IGNORECASE)
        for srcset in srcset_matches:
            # srcset可能包含多个URL，格式: "url1 1x, url2 2x"
            for url_part in srcset.split(','):
                url = url_part.strip().split(' ')[0]  # 取URL部分，忽略尺寸描述
                if self.is_valid_url(url):
                    urls.append(url)
        
        # 方法3: 按各种分隔符分割
        if not urls:
            separators = ['|||', '|', ',', ';', '><', '\n']
            for sep in separators:
                if sep in text:
                    parts = text.split(sep)
                    for part in parts:
                        cleaned = self.clean_url_part(part)
                        if cleaned and self.is_valid_url(cleaned):
                            urls.append(cleaned)
                    if urls:  # 找到URL就停止尝试其他分隔符
                        break
        
        return urls
    
    def clean_url_part(self, part: str) -> str:
        """清理URL片段"""
        # 移除HTML标签和属性
        part = re.sub(r'<[^>]*>', '', part)
        part = re.sub(r'\b(?:srcset|type|src)\s*=\s*["\']?', '', part)
        part = re.sub(r'["\']?\s*type\s*=\s*["\']?[^"\']*["\']?', '', part)
        
        # 清理前后的引号、空格和特殊字符
        part = part.strip().strip('"\'<>|')
        
        # 如果不是以http开头，尝试找到http开头的部分
        if not part.startswith('http'):
            http_match = re.search(r'(https?://[^\s<>"\'|]+)', part)
            if http_match:
                part = http_match.group(1)
        
        return part
    
    def is_valid_url(self, url: str) -> bool:
        """验证URL是否有效"""
        if not url or len(url) < 10:
            return False
        
        # 必须以http或https开头
        if not url.startswith(('http://', 'https://')):
            return False
        
        # 检查是否包含图片扩展名
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
        url_lower = url.lower()
        
        # 检查URL路径中是否包含图片扩展名
        try:
            parsed = urlparse(url_lower)
            path = parsed.path
            return any(path.endswith(ext) for ext in image_extensions)
        except:
            # 如果URL解析失败，使用简单的字符串检查
            return any(ext in url_lower for ext in image_extensions)
    
    def deduplicate_urls(self, urls: List[str]) -> List[str]:
        """去重URL，处理相同图片的不同版本"""
        if not urls:
            return []
        
        unique_urls = []
        seen_signatures = set()
        
        for url in urls:
            # 生成URL签名用于去重
            signature = self.generate_url_signature(url)
            
            if signature not in seen_signatures:
                unique_urls.append(url)
                seen_signatures.add(signature)
        
        return unique_urls
    
    def generate_url_signature(self, url: str) -> str:
        """生成URL签名用于去重"""
        try:
            parsed = urlparse(url.lower())
            
            # 使用域名 + 路径（不包含查询参数）作为基础签名
            base_signature = f"{parsed.netloc}{parsed.path}"
            
            # 移除可能的尺寸标识符
            base_signature = re.sub(r'[_-](?:thumb|small|large|medium|\d+x?\d*)', '', base_signature)
            
            return base_signature
        except:
            return url.lower()

def test_your_specific_case():
    """测试你的具体案例"""
    processor = OptimizedImageProcessor()
    
    test_input = 'https://media.cdn.bauhaus/m/1475267/12.webp" type="image/webp"><source srcset="https://media.cdn.bauhaus/m/1475267/12.jpg|https://media.cdn.bauhaus/m/1475267/12.jpg"|https://media.cdn.bauhaus/m/1474562/12.jpg"|https://media.cdn.bauhaus/m/1474286/12.jpg'
    
    print("🧪 测试你的具体案例")
    print("="*80)
    print("输入:")
    print(test_input)
    print("\n" + "-"*80)
    
    result = processor.process_complex_images(test_input)
    
    print("输出:")
    print(result)
    
    if result:
        urls = result.split(',')
        print(f"\n提取到 {len(urls)} 个唯一URL:")
        for i, url in enumerate(urls, 1):
            print(f"{i}. {url}")
    
    print("\n" + "="*80)
    print("✅ 测试完成")

if __name__ == "__main__":
    test_your_specific_case()
