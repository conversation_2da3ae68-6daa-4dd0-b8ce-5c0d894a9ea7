#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WooCommerce转换脚本 - 优化版本
解决SKU长度、分类处理、HTML标签清理等问题
"""

import pandas as pd
import numpy as np
import re
import hashlib
import time
import random
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional

class AdvancedWooCommerceConverter:
    def __init__(self):
        self.sku_counter = 1
        self.sku_prefix = "WC"  # 可以配置SKU前缀
        
    def generate_sku_optimized(self, product_name: str, brand: str = "") -> str:
        """
        1. SKU字段优化：生成更短的SKU
        格式: WC-{品牌缩写}-{产品关键词}-{序号}
        """
        # 清理产品名称，提取关键词
        clean_name = re.sub(r'[^\w\s]', '', str(product_name))
        words = clean_name.split()
        
        # 提取关键产品词（排除常见词）
        stop_words = {'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an'}
        key_words = [word for word in words if word.lower() not in stop_words and len(word) > 2]
        
        # 取前2-3个关键词，每个词取前3-4个字符
        product_part = ""
        for word in key_words[:3]:
            product_part += word[:4].upper()
            if len(product_part) >= 8:  # 限制产品部分长度
                break
        
        # 品牌处理
        brand_part = ""
        if brand and brand.strip():
            clean_brand = re.sub(r'[^\w]', '', str(brand))[:3].upper()
            brand_part = f"{clean_brand}-"
        
        # 生成SKU
        sku = f"{self.sku_prefix}-{brand_part}{product_part}-{self.sku_counter:03d}"
        
        # 确保SKU不超过20个字符
        if len(sku) > 20:
            sku = f"{self.sku_prefix}-{self.sku_counter:04d}"
        
        self.sku_counter += 1
        return sku
    
    def process_categories_advanced(self, row: Dict) -> tuple:
        """
        2. 分类处理优化：处理混乱的分类数据
        """
        category_field = row.get('tax:product_cat', '')
        
        if not category_field or str(category_field).strip() == 'nan':
            return "Uncategorized", "General"
        
        # 清理分类数据
        categories_str = str(category_field).strip()
        
        # 处理各种分隔符
        categories_str = re.sub(r'[|]{2,}', '|||', categories_str)  # 标准化分隔符
        categories_str = re.sub(r'[>]{2,}', ' > ', categories_str)  # 处理多余的分隔符
        categories_str = re.sub(r'[&]{2,}', ' & ', categories_str)  # 处理&符号
        
        # 分割分类
        if '|||' in categories_str:
            category_parts = categories_str.split('|||')
        elif ' > ' in categories_str:
            category_parts = categories_str.split(' > ')
        elif '&' in categories_str:
            category_parts = categories_str.split('&')
        else:
            category_parts = [categories_str]
        
        # 清理每个分类部分
        cleaned_parts = []
        for part in category_parts:
            part = part.strip()
            if part and part != 'nan' and len(part) > 1:
                # 移除特殊字符，保留字母数字和空格
                part = re.sub(r'[^\w\s\-&]', '', part)
                part = re.sub(r'\s+', ' ', part).strip()
                if part:
                    cleaned_parts.append(part)
        
        # 去重并限制层级
        unique_parts = []
        seen = set()
        for part in cleaned_parts:
            if part.lower() not in seen and len(unique_parts) < 4:  # 最多4级分类
                unique_parts.append(part)
                seen.add(part.lower())
        
        if not unique_parts:
            return "Uncategorized", "General"
        
        # 构建分类字符串
        categories = ' > '.join(unique_parts)
        
        # 生成标签（使用最后一个分类）
        tags = unique_parts[-1] if unique_parts else "General"
        
        return categories, tags
    
    def clean_html_for_woocommerce(self, html_content: str) -> str:
        """
        3. HTML标签清理：智能替换为WooCommerce兼容的HTML
        """
        if not html_content or str(html_content).strip() == 'nan':
            return ""
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(str(html_content), 'html.parser')
        
        # 移除危险标签
        dangerous_tags = ['script', 'style', 'iframe', 'noscript', 'svg', 'canvas', 'object', 'embed']
        for tag in dangerous_tags:
            for element in soup.find_all(tag):
                element.decompose()
        
        # 移除所有on*事件属性
        for element in soup.find_all():
            attrs_to_remove = []
            for attr in element.attrs:
                if attr.startswith('on'):
                    attrs_to_remove.append(attr)
            for attr in attrs_to_remove:
                del element.attrs[attr]
        
        # 处理链接标签
        for a_tag in soup.find_all('a'):
            # 移除链接，只保留文本
            a_tag.unwrap()
        
        # 处理图片标签
        for img_tag in soup.find_all('img'):
            # 移除图片标签
            img_tag.decompose()
        
        # 处理按钮标签
        for button_tag in soup.find_all('button'):
            button_tag.unwrap()
        
        # 替换font标签为span
        for font_tag in soup.find_all('font'):
            font_tag.name = 'span'
            # 移除样式属性
            if 'style' in font_tag.attrs:
                del font_tag.attrs['style']
            if 'color' in font_tag.attrs:
                del font_tag.attrs['color']
            if 'size' in font_tag.attrs:
                del font_tag.attrs['size']
        
        # 处理div标签
        for div_tag in soup.find_all('div'):
            # 检查是否是纯文本div
            if not div_tag.find(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'table']):
                div_tag.name = 'p'
            else:
                # 移除样式属性
                if 'style' in div_tag.attrs:
                    del div_tag.attrs['style']
                if 'class' in div_tag.attrs:
                    del div_tag.attrs['class']
        
        # 处理表格
        for table_tag in soup.find_all('table'):
            # 添加WooCommerce表格样式
            table_tag['class'] = 'woocommerce-table'
            # 移除样式属性
            if 'style' in table_tag.attrs:
                del table_tag.attrs['style']
            if 'width' in table_tag.attrs:
                del table_tag.attrs['width']
            if 'height' in table_tag.attrs:
                del table_tag.attrs['height']
        
        # 处理表格单元格
        for td_tag in soup.find_all(['td', 'th']):
            if 'style' in td_tag.attrs:
                del td_tag.attrs['style']
            if 'width' in td_tag.attrs:
                del td_tag.attrs['width']
            if 'height' in td_tag.attrs:
                del td_tag.attrs['height']
        
        # 处理列表
        for ul_tag in soup.find_all('ul'):
            if 'style' in ul_tag.attrs:
                del ul_tag.attrs['style']
        
        for ol_tag in soup.find_all('ol'):
            if 'style' in ol_tag.attrs:
                del ol_tag.attrs['style']
        
        # 处理标题标签
        for h_tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            if 'style' in h_tag.attrs:
                del h_tag.attrs['style']
            # 将h1, h2转换为h3, h4（WooCommerce推荐）
            if h_tag.name in ['h1', 'h2']:
                h_tag.name = 'h3'
            elif h_tag.name in ['h5', 'h6']:
                h_tag.name = 'h4'
        
        # 处理段落
        for p_tag in soup.find_all('p'):
            if 'style' in p_tag.attrs:
                del p_tag.attrs['style']
        
        # 处理强调标签
        for strong_tag in soup.find_all('strong'):
            if 'style' in strong_tag.attrs:
                del strong_tag.attrs['style']
        
        for em_tag in soup.find_all('em'):
            if 'style' in em_tag.attrs:
                del em_tag.attrs['style']
        
        # 处理换行标签
        for br_tag in soup.find_all('br'):
            if 'style' in br_tag.attrs:
                del br_tag.attrs['style']
        
        # 清理空白和多余换行
        text = str(soup)
        text = re.sub(r'\n\s*\n', '\n\n', text)  # 合并多个换行
        text = re.sub(r'[ \t]+', ' ', text)  # 合并多个空格
        text = text.strip()
        
        return text
    
    def generate_description_advanced(self, row: Dict) -> str:
        """
        4. 描述生成优化：使用HTML清理功能
        """
        # 优先级1：完整描述
        full_description = row.get('post_content', '')
        if full_description and str(full_description).strip() and str(full_description) != 'nan':
            cleaned_desc = self.clean_html_for_woocommerce(full_description)
            if len(cleaned_desc) > 50:
                return cleaned_desc
        
        # 优先级2：短描述
        short_description = row.get('post_excerpt', '')
        if short_description and str(short_description).strip() and str(short_description) != 'nan':
            cleaned_short = self.clean_html_for_woocommerce(short_description)
            if len(cleaned_short) > 20:
                return cleaned_short
        
        # 优先级3：优化产品标题作为描述
        product_title = row.get('post_title', '')
        if product_title and str(product_title).strip():
            return self.optimize_title_as_description(str(product_title))
        
        return "优质产品，欢迎选购"
    
    def optimize_title_as_description(self, title: str) -> str:
        """将产品标题优化为描述"""
        clean_title = re.sub(r'[^\w\s\-\.]', '', title)
        clean_title = re.sub(r'\s+', ' ', clean_title.strip())
        
        # 生成带HTML标签的描述
        descriptions = [
            f"<p>探索我们的<strong>{clean_title}</strong>，为您提供优质的产品体验。</p>",
            f"<p>选择<strong>{clean_title}</strong>，享受专业品质和可靠性能。</p>",
            f"<p>我们的<strong>{clean_title}</strong>经过精心挑选，确保满足您的需求。</p>",
            f"<p>发现<strong>{clean_title}</strong>的卓越品质，为您的项目提供完美解决方案。</p>",
            f"<p>体验<strong>{clean_title}</strong>带来的专业级性能和可靠性。</p>"
        ]
        
        if len(clean_title) > 50:
            return descriptions[0]
        elif len(clean_title) > 30:
            return descriptions[1]
        else:
            return descriptions[2]
    
    def convert_row_to_wc_advanced(self, row: Dict) -> Dict:
        """高级行转换函数"""
        wc_row = {}
        
        # 1. 生成优化的SKU
        wc_row['SKU'] = self.generate_sku_optimized(
            row.get('post_title', ''),
            row.get('attribute:Brand', '')
        )
        
        # 2. 产品名称
        wc_row['Name'] = self.clean_text(row.get('post_title', ''))
        
        # 3. 生成优化的描述
        wc_row['Description'] = self.generate_description_advanced(row)
        
        # 4. 处理价格
        original_price = pd.to_numeric(row.get('regular_price', 0), errors='coerce')
        wc_row['Regular price'] = self.calculate_regular_price(original_price)
        wc_row['Sale price'] = self.calculate_sale_price(wc_row['Regular price'])
        
        # 5. 处理分类和标签
        categories, tags = self.process_categories_advanced(row)
        wc_row['Categories'] = categories
        wc_row['Tags'] = tags
        
        # 6. 图片处理
        wc_row['Images'] = self.process_images(
            row.get('images', ''),
            row.get('featured', '')
        )
        
        # 7. 其他字段...
        wc_row['Type'] = 'simple'
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['In stock?'] = 1
        wc_row['Backorders allowed?'] = 1
        wc_row['Sold individually?'] = 0
        wc_row['Allow customer reviews?'] = 1
        wc_row['Position'] = 0
        
        # 属性字段
        wc_row['Attribute 1 name'] = 'Brand'
        wc_row['Attribute 1 value(s)'] = self.clean_text(row.get('attribute:Brand', ''))
        wc_row['Attribute 1 visible'] = 1
        wc_row['Attribute 1 global'] = 0
        
        wc_row['Attribute 2 name'] = 'MPN'
        wc_row['Attribute 2 value(s)'] = self.clean_text(row.get('attribute:MPN', ''))
        wc_row['Attribute 2 visible'] = 1
        wc_row['Attribute 2 global'] = 0
        
        wc_row['Attribute 3 name'] = 'UPC'
        wc_row['Attribute 3 value(s)'] = self.clean_text(row.get('attribute:UPC', ''))
        wc_row['Attribute 3 visible'] = 1
        wc_row['Attribute 3 global'] = 0
        
        wc_row['Brands'] = self.clean_text(row.get('attribute:Brand', ''))
        
        # 其他字段设为空
        for field in ['ID', 'Short description', 'Date sale price starts', 'Date sale price ends', 
                     'Tax status', 'Tax class', 'Stock', 'Low stock amount', 'Weight (kg)', 
                     'Length (cm)', 'Width (cm)', 'Height (cm)', 'Purchase note', 'Shipping class',
                     'Download limit', 'Download expiry days', 'Parent', 'Grouped products', 
                     'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Attribute 4 name',
                     'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
                     'Meta: rank_math_focus_keyword']:
            wc_row[field] = ''
        
        return wc_row
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if pd.isna(text) or text == '':
            return ''
        
        text = re.sub(r'<[^>]+>', '', str(text))
        text = re.sub(r'[^\w\s\-\.\,\|\&\#\(\)]', '', str(text))
        return str(text).strip()
    
    def calculate_regular_price(self, original_price: float) -> float:
        """计算常规价格"""
        if pd.isna(original_price) or original_price <= 0:
            return 0.0
        
        if original_price < 3:
            return round(original_price, 2)
        
        if original_price <= 10:
            discount = 1
        elif original_price <= 50:
            discount = 2
        else:
            discount = 3
        
        new_price = original_price - discount
        return round(max(new_price, 0.01), 2)
    
    def calculate_sale_price(self, regular_price: float) -> float:
        """计算促销价格"""
        if pd.isna(regular_price) or regular_price <= 0:
            return 0.0
        
        if regular_price <= 50:
            sale_price = regular_price * 0.56
        elif regular_price <= 100:
            sale_price = regular_price * 0.5
        elif regular_price <= 150:
            sale_price = regular_price * 0.45
        elif regular_price <= 200:
            sale_price = regular_price * 0.42
        elif regular_price <= 250:
            sale_price = regular_price * 0.36
        elif regular_price <= 300:
            sale_price = regular_price * 0.33
        elif regular_price <= 350:
            sale_price = regular_price * 0.31
        elif regular_price <= 500:
            sale_price = regular_price * 0.26
        elif regular_price <= 750:
            sale_price = regular_price * 0.24
        elif regular_price <= 1000:
            sale_price = regular_price * 0.22
        elif regular_price <= 2000:
            sale_price = regular_price * 0.2
        else:
            sale_price = regular_price * 0.15
        
        return round(sale_price, 2)
    
    def process_images(self, images_str: str, featured_str: str) -> str:
        """处理图片字段"""
        if pd.isna(images_str) or images_str == '':
            if pd.isna(featured_str) or featured_str == '':
                return ''
            return str(featured_str)
        
        if '|||' in str(images_str):
            return str(images_str).split('|||')[0]
        
        return str(images_str)

def test_advanced_optimizations():
    """测试高级优化功能"""
    print("🧪 测试高级优化功能...")
    print("="*60)
    
    converter = AdvancedWooCommerceConverter()
    
    # 测试数据
    test_row = {
        'post_title': 'Globe Scientific Serological Pipette, 1mL, PS, Quality Tip, 275mm, STERILE, Yellow Band',
        'post_content': '<div style="color:red;"><h1>Product Description</h1><p>This is a <strong>high quality</strong> product.</p><a href="http://ebay.com">Click here</a><img src="image.jpg"><script>alert("test");</script></div>',
        'post_excerpt': '<p>Short description with <em>emphasis</em></p>',
        'regular_price': 103.28,
        'tax:product_cat': 'Environmental & Waste Management|||Laboratory Equipment & Supplies|||Laboratory Supplies & Consumables',
        'images': 'https://example.com/image1.jpg|||https://example.com/image2.jpg',
        'attribute:Brand': 'Globe Scientific',
        'attribute:MPN': '1700',
        'attribute:UPC': '840095621823'
    }
    
    # 测试各个优化功能
    print("1. 优化SKU生成测试:")
    sku = converter.generate_sku_optimized(test_row['post_title'], test_row['attribute:Brand'])
    print(f"   生成SKU: {sku} (长度: {len(sku)})")
    
    print("\\n2. 分类处理测试:")
    categories, tags = converter.process_categories_advanced(test_row)
    print(f"   分类: {categories}")
    print(f"   标签: {tags}")
    
    print("\\n3. HTML清理测试:")
    cleaned_html = converter.clean_html_for_woocommerce(test_row['post_content'])
    print(f"   清理前: {test_row['post_content'][:100]}...")
    print(f"   清理后: {cleaned_html[:100]}...")
    
    print("\\n4. 描述生成测试:")
    description = converter.generate_description_advanced(test_row)
    print(f"   生成描述: {description[:150]}...")
    
    print("\\n5. 完整转换测试:")
    wc_row = converter.convert_row_to_wc_advanced(test_row)
    print(f"   SKU: {wc_row['SKU']} (长度: {len(wc_row['SKU'])})")
    print(f"   Name: {wc_row['Name'][:50]}...")
    print(f"   Categories: {wc_row['Categories']}")
    print(f"   Tags: {wc_row['Tags']}")
    print(f"   Description: {wc_row['Description'][:100]}...")

if __name__ == "__main__":
    test_advanced_optimizations()
