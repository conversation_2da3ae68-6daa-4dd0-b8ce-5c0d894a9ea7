#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级转换器 - 将各种电商数据转换为WooCommerce格式
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import html
from urllib.parse import urlparse
import hashlib
import json

class AdvancedConverter:
    def __init__(self):
        self.woocommerce_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Meta: _yoast_wpseo_title', 'Meta: _yoast_wpseo_metadesc', 'Meta: _yoast_wpseo_focuskw'
        ]
        
        self.field_mappings = {
            # 产品名称映射
            'name': ['name', 'title', 'product_name', 'product_title', 'item_name', 'product'],
            # 描述映射
            'description': ['description', 'desc', 'product_description', 'long_description', 'details'],
            'short_description': ['short_description', 'short_desc', 'summary', 'excerpt'],
            # 价格映射
            'regular_price': ['price', 'regular_price', 'list_price', 'msrp', 'original_price'],
            'sale_price': ['sale_price', 'discount_price', 'special_price', 'offer_price'],
            # SKU映射
            'sku': ['sku', 'product_id', 'item_id', 'model', 'part_number'],
            # 分类映射
            'categories': ['category', 'categories', 'product_category', 'type', 'classification'],
            # 图片映射
            'images': ['image', 'images', 'photo', 'picture', 'image_url', 'main_image'],
            # 库存映射
            'stock': ['stock', 'quantity', 'inventory', 'qty', 'available'],
            # 重量映射
            'weight': ['weight', 'shipping_weight', 'product_weight'],
            # 标签映射
            'tags': ['tags', 'keywords', 'labels', 'attributes']
        }

    def detect_columns(self, df: pd.DataFrame):
        """智能检测列映射"""
        detected_mapping = {}
        
        for wc_field, possible_names in self.field_mappings.items():
            for col in df.columns:
                col_lower = str(col).lower().strip()
                
                # 精确匹配
                if col_lower in [name.lower() for name in possible_names]:
                    detected_mapping[wc_field] = col
                    break
                
                # 模糊匹配
                for possible_name in possible_names:
                    if possible_name.lower() in col_lower or col_lower in possible_name.lower():
                        if wc_field not in detected_mapping:  # 避免重复映射
                            detected_mapping[wc_field] = col
                            break
        
        return detected_mapping

    def clean_html_content(self, content):
        """清理HTML内容"""
        if pd.isna(content) or content == '':
            return ''
        
        content = str(content)
        
        # 解码HTML实体
        content = html.unescape(content)
        
        # 清理常见的HTML标签但保留基本格式
        content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
        content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
        
        # 保留基本的HTML标签
        allowed_tags = ['p', 'br', 'strong', 'b', 'em', 'i', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
        
        # 移除不允许的标签但保留内容
        content = re.sub(r'<(?!/?(?:' + '|'.join(allowed_tags) + r')\b)[^>]+>', '', content)
        
        # 清理多余的空白
        content = re.sub(r'\s+', ' ', content).strip()
        
        return content

    def generate_sku(self, name, index=None):
        """生成SKU"""
        if pd.isna(name) or name == '':
            name = f"PRODUCT_{index}" if index else "PRODUCT"
        
        # 清理产品名称
        clean_name = re.sub(r'[^\w\s-]', '', str(name))
        clean_name = re.sub(r'\s+', '-', clean_name.strip())
        
        # 生成基于名称的哈希
        name_hash = hashlib.md5(str(name).encode()).hexdigest()[:6].upper()
        
        # 组合SKU
        sku = f"{clean_name[:20].upper()}-{name_hash}"
        
        return sku

    def process_images(self, image_data):
        """处理图片URL"""
        if pd.isna(image_data) or image_data == '':
            return ''
        
        image_str = str(image_data)
        
        # 查找URL模式
        url_pattern = r'https?://[^\s,;|]+'
        urls = re.findall(url_pattern, image_str)
        
        if urls:
            # 验证URL格式
            valid_urls = []
            for url in urls:
                try:
                    parsed = urlparse(url)
                    if parsed.scheme and parsed.netloc:
                        valid_urls.append(url)
                except:
                    continue
            
            return ', '.join(valid_urls[:5])  # 最多5张图片
        
        return ''

    def categorize_product(self, name, description='', existing_category=''):
        """智能产品分类"""
        if existing_category and str(existing_category).strip():
            return str(existing_category).strip()
        
        text_to_analyze = f"{name} {description}".lower()
        
        category_keywords = {
            'Home & Garden > Furniture': ['chair', 'table', 'desk', 'bed', 'sofa', 'cabinet', 'furniture'],
            'Electronics > Computers': ['computer', 'laptop', 'desktop', 'monitor', 'keyboard', 'mouse'],
            'Electronics > Phones': ['phone', 'smartphone', 'mobile', 'iphone', 'android'],
            'Clothing > Men': ['men', 'male', 'gentleman', 'mens'],
            'Clothing > Women': ['women', 'female', 'ladies', 'womens'],
            'Sports & Outdoors': ['sport', 'outdoor', 'fitness', 'exercise', 'camping', 'hiking'],
            'Tools & Hardware': ['tool', 'hardware', 'drill', 'hammer', 'wrench', 'equipment'],
            'Automotive': ['car', 'auto', 'vehicle', 'automotive', 'motor', 'parts'],
            'Beauty & Personal Care': ['beauty', 'cosmetic', 'skincare', 'makeup', 'personal care'],
            'Toys & Games': ['toy', 'game', 'kids', 'children', 'play', 'educational']
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in text_to_analyze for keyword in keywords):
                return category
        
        return 'Uncategorized'

    def convert_to_woocommerce(self, input_file: str, output_file: str = None):
        """转换为WooCommerce格式"""
        try:
            print(f"🔄 开始转换: {Path(input_file).name}")
            
            # 读取数据
            if input_file.endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)
            
            print(f"📊 原始数据: {len(df)} 行, {len(df.columns)} 列")
            
            # 检测列映射
            column_mapping = self.detect_columns(df)
            print(f"🔍 检测到的列映射: {len(column_mapping)} 个字段")
            
            # 创建WooCommerce DataFrame
            wc_df = pd.DataFrame(columns=self.woocommerce_columns)
            
            # 基本字段映射
            for i, row in df.iterrows():
                wc_row = {}
                
                # ID
                wc_row['ID'] = i + 1
                wc_row['Type'] = 'simple'
                
                # 产品名称
                if 'name' in column_mapping:
                    wc_row['Name'] = str(row[column_mapping['name']]) if pd.notna(row[column_mapping['name']]) else f"Product {i+1}"
                else:
                    wc_row['Name'] = f"Product {i+1}"
                
                # SKU
                if 'sku' in column_mapping:
                    wc_row['SKU'] = str(row[column_mapping['sku']]) if pd.notna(row[column_mapping['sku']]) else self.generate_sku(wc_row['Name'], i+1)
                else:
                    wc_row['SKU'] = self.generate_sku(wc_row['Name'], i+1)
                
                # 描述
                if 'description' in column_mapping:
                    desc = self.clean_html_content(row[column_mapping['description']])
                    wc_row['Description'] = f"<p>{desc}</p>" if desc else ""
                else:
                    wc_row['Description'] = ""
                
                if 'short_description' in column_mapping:
                    wc_row['Short description'] = str(row[column_mapping['short_description']]) if pd.notna(row[column_mapping['short_description']]) else ""
                else:
                    wc_row['Short description'] = ""
                
                # 价格
                if 'regular_price' in column_mapping:
                    price = row[column_mapping['regular_price']]
                    wc_row['Regular price'] = float(price) if pd.notna(price) and str(price).replace('.', '').isdigit() else ""
                else:
                    wc_row['Regular price'] = ""
                
                if 'sale_price' in column_mapping:
                    sale_price = row[column_mapping['sale_price']]
                    wc_row['Sale price'] = float(sale_price) if pd.notna(sale_price) and str(sale_price).replace('.', '').isdigit() else ""
                else:
                    wc_row['Sale price'] = ""
                
                # 分类
                if 'categories' in column_mapping:
                    existing_cat = row[column_mapping['categories']]
                    wc_row['Categories'] = self.categorize_product(wc_row['Name'], wc_row['Description'], existing_cat)
                else:
                    wc_row['Categories'] = self.categorize_product(wc_row['Name'], wc_row['Description'])
                
                # 图片
                if 'images' in column_mapping:
                    wc_row['Images'] = self.process_images(row[column_mapping['images']])
                else:
                    wc_row['Images'] = ""
                
                # 库存
                if 'stock' in column_mapping:
                    stock = row[column_mapping['stock']]
                    wc_row['Stock'] = int(stock) if pd.notna(stock) and str(stock).isdigit() else 100
                else:
                    wc_row['Stock'] = 100
                
                # 默认设置
                wc_row['Published'] = 1
                wc_row['Is featured?'] = 0
                wc_row['Visibility in catalog'] = 'visible'
                wc_row['Tax status'] = 'taxable'
                wc_row['In stock?'] = 1
                wc_row['Allow customer reviews?'] = 1
                wc_row['Backorders allowed?'] = 0
                wc_row['Sold individually?'] = 0
                
                # SEO字段
                wc_row['Meta: _yoast_wpseo_title'] = wc_row['Name']
                wc_row['Meta: _yoast_wpseo_metadesc'] = wc_row['Short description'] or f"High-quality {wc_row['Name']} available now"
                wc_row['Meta: _yoast_wpseo_focuskw'] = wc_row['Name'].split()[0].lower() if wc_row['Name'] else ""
                
                # 添加到DataFrame
                wc_df = pd.concat([wc_df, pd.DataFrame([wc_row])], ignore_index=True)
            
            # 保存结果
            if output_file is None:
                input_path = Path(input_file)
                output_file = f"woocommerce_{input_path.stem}.csv"
            
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 转换完成!")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 转换结果: {len(wc_df)} 个产品")
            
            return output_file
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def batch_convert(self, input_directory: str, output_directory: str = None):
        """批量转换"""
        input_dir = Path(input_directory)
        
        if not input_dir.exists():
            print(f"❌ 输入目录不存在: {input_directory}")
            return
        
        if output_directory is None:
            output_directory = "woocommerce_output"
        
        output_dir = Path(output_directory)
        output_dir.mkdir(exist_ok=True)
        
        # 查找数据文件
        data_files = list(input_dir.glob("*.csv")) + list(input_dir.glob("*.xlsx"))
        
        if not data_files:
            print(f"❌ 目录中没有数据文件")
            return
        
        print(f"🚀 批量转换开始")
        print(f"输入目录: {input_directory}")
        print(f"输出目录: {output_directory}")
        print(f"文件数量: {len(data_files)}")
        
        converted_files = []
        
        for i, file_path in enumerate(data_files, 1):
            print(f"\n[{i}/{len(data_files)}] " + "="*50)
            
            output_file = output_dir / f"woocommerce_{file_path.stem}.csv"
            result = self.convert_to_woocommerce(str(file_path), str(output_file))
            
            if result:
                converted_files.append(result)
        
        print(f"\n🎉 批量转换完成!")
        print(f"成功转换: {len(converted_files)} 个文件")
        
        return converted_files

def main():
    """主函数"""
    converter = AdvancedConverter()
    
    print("🔄 高级WooCommerce转换器")
    print("="*50)
    
    print("📋 使用方法:")
    print("1. 单文件转换:")
    print("   converter.convert_to_woocommerce('input.csv', 'output.csv')")
    print("2. 批量转换:")
    print("   converter.batch_convert('input_directory', 'output_directory')")

if __name__ == "__main__":
    main()
