#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极德国电商数据转换器 - 结合所有版本的优点
"""

import pandas as pd
import re
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateDEConverter:
    def __init__(self):
        self.sku_counter = 1
        self.sku_prefix = "DE"
        self.used_skus = set()
        
        # 停用词
        self.stop_words = {
            'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an',
            'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'und', 'oder', 'mit',
            'von', 'zu', 'auf', 'bei', 'nach', 'vor', 'über', 'unter', 'durch', 'für'
        }
        
        # 字段映射
        self.field_mappings = {
            'id_fields': ['ID', 'id', 'product_id', 'post_id', 'sku', 'SKU', 'code', 'Code'],
            'name_fields': ['Name', 'name', 'title', 'Title', 'post_title', 'product_name', 'Produktname'],
            'price_fields': ['Regular price', 'price', 'Price', 'Preis', 'regular_price', 'cost'],
            'sale_price_fields': ['Sale price', 'sale_price', 'discount_price', 'Angebotspreis'],
            'description_fields': ['Description', 'description', 'post_content', 'content', 'Beschreibung'],
            'short_desc_fields': ['Short description', 'short_description', 'post_excerpt', 'excerpt'],
            'category_fields': ['Categories', 'category', 'categories', 'tax:product_cat', 'Kategorie'],
            'image_fields': ['Images', 'images', 'image', 'featured_image', 'Bilder'],
            'brand_fields': ['Brand', 'brand', 'manufacturer', 'Marke', 'Hersteller']
        }

    def analyze_first_column(self, df: pd.DataFrame) -> Dict:
        """分析第一列的数据类型和用途"""
        first_col = df.iloc[:, 0]
        first_col_name = df.columns[0]
        
        analysis = {
            'column_name': first_col_name,
            'data_type': str(first_col.dtype),
            'sample_values': first_col.head().tolist(),
            'unique_count': first_col.nunique(),
            'null_count': first_col.isnull().sum(),
            'likely_purpose': 'unknown'
        }
        
        # 判断第一列的可能用途
        col_name_lower = first_col_name.lower()
        
        if any(keyword in col_name_lower for keyword in ['id', 'sku', 'code']):
            analysis['likely_purpose'] = 'product_id'
        elif any(keyword in col_name_lower for keyword in ['name', 'title', 'product']):
            analysis['likely_purpose'] = 'product_name'
        elif first_col.nunique() == len(first_col) and not first_col.isnull().any():
            analysis['likely_purpose'] = 'unique_identifier'
        elif first_col.dtype in ['int64', 'float64'] and first_col.nunique() == len(first_col):
            analysis['likely_purpose'] = 'numeric_id'
        
        return analysis

    def get_field_value(self, row: pd.Series, field_list: List[str]) -> str:
        """从多个可能的字段中获取值"""
        for field in field_list:
            if field in row.index and pd.notna(row[field]) and str(row[field]).strip():
                return str(row[field]).strip()
        return ''

    def extract_keywords(self, text: str, max_words: int = 2) -> List[str]:
        """提取关键词"""
        if not text or str(text).strip() == 'nan':
            return []
        
        clean_text = re.sub(r'[^\w\s]', ' ', str(text).lower())
        words = clean_text.split()
        
        keywords = []
        for word in words:
            if (len(word) > 2 and 
                word not in self.stop_words and 
                not word.isdigit() and
                len(keywords) < max_words):
                keywords.append(word)
        
        return keywords

    def generate_smart_sku(self, row: pd.Series, first_col_analysis: Dict) -> str:
        """智能SKU生成"""
        
        # 策略1: 使用第一列（如果适合作为ID）
        if first_col_analysis['likely_purpose'] in ['product_id', 'unique_identifier', 'numeric_id']:
            first_col_value = str(row.iloc[0]).strip()
            if first_col_value and first_col_value != 'nan':
                clean_id = re.sub(r'[^\w\-]', '', first_col_value)
                if len(clean_id) <= 12:
                    sku = f"{self.sku_prefix}-{clean_id}"
                    if sku not in self.used_skus:
                        self.used_skus.add(sku)
                        return sku[:20]
        
        # 策略2: 使用传统ID字段
        original_id = self.get_field_value(row, self.field_mappings['id_fields'])
        if original_id:
            clean_id = re.sub(r'[^\w\-]', '', original_id)
            if len(clean_id) <= 12:
                sku = f"{self.sku_prefix}-{clean_id}"
                if sku not in self.used_skus:
                    self.used_skus.add(sku)
                    return sku[:20]
        
        # 策略3: 基于产品名称和品牌生成
        name = self.get_field_value(row, self.field_mappings['name_fields'])
        brand = self.get_field_value(row, self.field_mappings['brand_fields'])
        
        components = [self.sku_prefix]
        
        # 品牌部分
        if brand:
            brand_clean = re.sub(r'[^\w]', '', brand)[:4].upper()
            if brand_clean:
                components.append(brand_clean)
        
        # 产品关键词部分
        if name:
            keywords = self.extract_keywords(name, 2)
            if keywords:
                product_part = ''.join([word[:4].upper() for word in keywords])
                components.append(product_part[:8])
            else:
                clean_name = re.sub(r'[^\w]', '', name)[:6].upper()
                if clean_name:
                    components.append(clean_name)
        
        # 序号部分
        components.append(f"{self.sku_counter:03d}")
        
        sku = '-'.join(components)
        
        # 长度控制
        if len(sku) > 20:
            if brand:
                brand_part = re.sub(r'[^\w]', '', brand)[:3].upper()
                name_part = re.sub(r'[^\w]', '', name)[:4].upper()
                sku = f"{self.sku_prefix}-{brand_part}-{name_part}-{self.sku_counter:03d}"
            else:
                name_part = re.sub(r'[^\w]', '', name)[:6].upper()
                sku = f"{self.sku_prefix}-{name_part}-{self.sku_counter:03d}"
        
        # 确保唯一性
        original_sku = sku
        counter = 1
        while sku in self.used_skus:
            sku = f"{original_sku[:-3]}{counter:03d}"
            counter += 1
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:20]

    def process_file(self, input_file: Path) -> bool:
        """处理单个文件"""
        try:
            logger.info(f"开始处理文件: {input_file.name}")
            
            # 读取文件
            df = pd.read_excel(input_file)
            logger.info(f"读取到 {len(df)} 行数据")
            
            # 分析第一列
            first_col_analysis = self.analyze_first_column(df)
            logger.info(f"第一列分析: {first_col_analysis['column_name']} -> {first_col_analysis['likely_purpose']}")
            
            # 处理每一行
            processed_rows = []
            for index, row in df.iterrows():
                if index % 1000 == 0:
                    logger.info(f"已处理 {index} 行...")
                
                # 生成智能SKU
                sku = self.generate_smart_sku(row, first_col_analysis)
                
                # 构建WooCommerce行
                wc_row = {
                    'SKU': sku,
                    'Name': self.get_field_value(row, self.field_mappings['name_fields']),
                    'Regular price': self.get_field_value(row, self.field_mappings['price_fields']),
                    'Sale price': self.get_field_value(row, self.field_mappings['sale_price_fields']),
                    'Description': self.get_field_value(row, self.field_mappings['description_fields']),
                    'Categories': self.get_field_value(row, self.field_mappings['category_fields']),
                    'Images': self.get_field_value(row, self.field_mappings['image_fields']),
                    'Type': 'simple',
                    'Published': 1,
                    'In stock?': 1,
                    'Stock': 999
                }
                
                processed_rows.append(wc_row)
            
            # 保存结果
            output_file = Path("woocommerce_output_ultimate") / f"{input_file.stem}_ultimate.csv"
            output_file.parent.mkdir(exist_ok=True)
            
            result_df = pd.DataFrame(processed_rows)
            result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            logger.info(f"转换完成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return False

def main():
    """主函数"""
    converter = UltimateDEConverter()
    
    # 获取Excel文件
    excel_files = list(Path('.').glob('*.xlsx'))
    
    if not excel_files:
        logger.error("没有找到Excel文件!")
        return
    
    logger.info(f"找到 {len(excel_files)} 个文件")
    
    # 处理文件
    success_count = 0
    for file in excel_files[:3]:  # 先处理前3个文件测试
        if converter.process_file(file):
            success_count += 1
    
    logger.info(f"处理完成: {success_count}/{len(excel_files[:3])}")

if __name__ == "__main__":
    main()
