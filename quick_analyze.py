#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from pathlib import Path

# 分析一个文件
file_path = Path("源数据文件/de/bauhaus-at-de-图片前两图.xlsx")

try:
    print(f"分析文件: {file_path.name}")
    
    # 读取Excel文件的第一个sheet
    df = pd.read_excel(file_path, nrows=2)
    
    print(f"列数: {len(df.columns)}")
    print(f"行数: {len(df)}")
    
    print("\n所有列名:")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")
    
    print(f"\n第一行数据示例:")
    if len(df) > 0:
        row = df.iloc[0]
        for col in df.columns[:15]:  # 只显示前15列
            value = str(row[col])[:50]
            print(f"{col}: {value}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
