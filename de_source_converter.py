#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德国源数据专用转换脚本
适配 源数据文件/de/ 目录下的Excel文件格式
"""

import pandas as pd
import numpy as np
import re
import os
import logging
from pathlib import Path
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DESourceConverter:
    def __init__(self):
        self.sku_counter = 1
        self.sku_prefix = "DE"
        
        # WooCommerce完整列定义
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
            'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
            'Brands', 'Meta: rank_math_focus_keyword'
        ]
        
        # 字段映射配置
        self.field_mappings = {
            # 产品名称字段
            'name_fields': ['title', 'post_title', 'Name'],
            # 价格字段
            'price_fields': ['price', 'regular_price', 'price/100'],
            # 描述字段
            'description_fields': ['description', 'detail', 'post_content'],
            # 短描述字段
            'short_desc_fields': ['description s', 'post_excerpt', 'short_description'],
            # 分类字段
            'category_fields': ['category', 'cate', 'tax:product_cat'],
            # 图片字段
            'image_fields': ['image', 'images', 'image0', 'featured'],
            # SKU字段
            'sku_fields': ['sku', 'SKU', 'ID'],
            # 品牌字段
            'brand_fields': ['Brand', 'attribute:Brand', 'brand'],
            # MPN字段
            'mpn_fields': ['mpn', 'MPN', 'MFG'],
            # UPC字段
            'upc_fields': ['UPC', 'upc', 'barcode']
        }
    
    def detect_file_format(self, df: pd.DataFrame) -> str:
        """检测文件格式类型"""
        columns = [str(col).lower() for col in df.columns]
        
        if 'price/100' in columns:
            return 'bauhaus_format'
        elif 'description s' in columns:
            return 'bloomled_format'
        elif 'post_title' in columns:
            return 'wordpress_format'
        else:
            return 'generic_format'
    
    def get_field_value(self, row: Dict, field_list: List[str]) -> str:
        """从行数据中获取字段值"""
        for field in field_list:
            if field in row and pd.notna(row[field]) and str(row[field]).strip():
                return str(row[field]).strip()
        return ''
    
    def generate_sku(self, name: str, brand: str = "", original_id: str = "") -> str:
        """生成SKU"""
        if original_id and str(original_id).strip():
            return f"{self.sku_prefix}-{str(original_id).strip()}"
        
        # 基于产品名称生成
        clean_name = re.sub(r'[^\w\s]', '', str(name))
        words = clean_name.split()[:3]  # 取前3个词
        name_part = ''.join([word[:3].upper() for word in words])
        
        if brand:
            brand_part = re.sub(r'[^\w]', '', str(brand))[:3].upper()
            sku = f"{self.sku_prefix}-{brand_part}-{name_part}-{self.sku_counter:04d}"
        else:
            sku = f"{self.sku_prefix}-{name_part}-{self.sku_counter:04d}"
        
        self.sku_counter += 1
        return sku[:20]  # 限制长度
    
    def process_price(self, price_value: str, file_format: str) -> float:
        """处理价格字段"""
        if pd.isna(price_value) or not str(price_value).strip():
            return 0.0
        
        try:
            price = float(str(price_value).replace(',', '.'))
            
            # 特殊格式处理
            if file_format == 'bauhaus_format':
                price = price / 100  # price/100 字段需要除以100
            
            return round(price, 2)
        except:
            return 0.0
    
    def process_categories(self, category_str: str) -> str:
        """处理分类字段"""
        if pd.isna(category_str) or not str(category_str).strip():
            return "Uncategorized"
        
        categories = str(category_str).strip()
        
        # 处理不同的分隔符
        if '>' in categories:
            # 格式: Bedroom>Beds>Fabric Beds
            parts = [part.strip() for part in categories.split('>') if part.strip()]
        elif '|||' in categories:
            parts = [part.strip() for part in categories.split('|||') if part.strip()]
        elif ',' in categories:
            parts = [part.strip() for part in categories.split(',') if part.strip()]
        else:
            parts = [categories]
        
        # 清理和去重
        cleaned_parts = []
        seen = set()
        for part in parts:
            if part and part.lower() not in seen:
                cleaned_parts.append(part)
                seen.add(part.lower())
        
        return ' > '.join(cleaned_parts) if cleaned_parts else "Uncategorized"
    
    def process_images(self, image_str: str) -> str:
        """处理图片字段"""
        if pd.isna(image_str) or not str(image_str).strip():
            return ''
        
        images = str(image_str).strip()
        
        # 处理多种分隔符
        if '|' in images:
            image_list = [img.strip() for img in images.split('|') if img.strip()]
        elif ',' in images:
            image_list = [img.strip() for img in images.split(',') if img.strip()]
        else:
            image_list = [images]
        
        # 验证URL并去重
        valid_images = []
        seen = set()
        for img in image_list:
            if img.startswith('http') and img not in seen:
                valid_images.append(img)
                seen.add(img)
        
        return ','.join(valid_images[:5])  # 最多5张图片
    
    def clean_html(self, html_str: str) -> str:
        """清理HTML内容"""
        if pd.isna(html_str) or not str(html_str).strip():
            return ''
        
        text = str(html_str)
        # 简单的HTML标签清理
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def convert_row_to_wc(self, row: Dict, file_format: str) -> Dict:
        """将源数据行转换为WooCommerce格式"""
        wc_row = {}
        
        # 基础字段
        wc_row['ID'] = ''
        wc_row['Type'] = 'simple'
        
        # 产品名称
        name = self.get_field_value(row, self.field_mappings['name_fields'])
        wc_row['Name'] = self.clean_html(name)
        
        # SKU
        original_sku = self.get_field_value(row, self.field_mappings['sku_fields'])
        brand = self.get_field_value(row, self.field_mappings['brand_fields'])
        wc_row['SKU'] = self.generate_sku(name, brand, original_sku)
        
        # 价格
        price_value = self.get_field_value(row, self.field_mappings['price_fields'])
        regular_price = self.process_price(price_value, file_format)
        wc_row['Regular price'] = regular_price
        wc_row['Sale price'] = round(regular_price * 0.8, 2) if regular_price > 0 else 0  # 8折促销价
        
        # 描述
        description = self.get_field_value(row, self.field_mappings['description_fields'])
        short_desc = self.get_field_value(row, self.field_mappings['short_desc_fields'])
        
        wc_row['Description'] = self.clean_html(description)
        wc_row['Short description'] = self.clean_html(short_desc)
        
        # 分类
        category = self.get_field_value(row, self.field_mappings['category_fields'])
        wc_row['Categories'] = self.process_categories(category)
        wc_row['Tags'] = wc_row['Categories'].split(' > ')[-1] if wc_row['Categories'] != "Uncategorized" else ""
        
        # 图片
        image = self.get_field_value(row, self.field_mappings['image_fields'])
        wc_row['Images'] = self.process_images(image)
        
        # 其他字段
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['In stock?'] = 1
        wc_row['Stock'] = ''
        wc_row['Low stock amount'] = ''
        wc_row['Backorders allowed?'] = 0
        wc_row['Sold individually?'] = 0
        wc_row['Allow customer reviews?'] = 1
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        
        # 属性字段
        wc_row['Attribute 1 name'] = 'Brand'
        wc_row['Attribute 1 value(s)'] = brand
        wc_row['Attribute 1 visible'] = 1
        wc_row['Attribute 1 global'] = 0
        
        mpn = self.get_field_value(row, self.field_mappings['mpn_fields'])
        wc_row['Attribute 2 name'] = 'MPN'
        wc_row['Attribute 2 value(s)'] = mpn
        wc_row['Attribute 2 visible'] = 1
        wc_row['Attribute 2 global'] = 0
        
        upc = self.get_field_value(row, self.field_mappings['upc_fields'])
        wc_row['Attribute 3 name'] = 'UPC'
        wc_row['Attribute 3 value(s)'] = upc
        wc_row['Attribute 3 visible'] = 1
        wc_row['Attribute 3 global'] = 0
        
        wc_row['Brands'] = brand
        
        # 其他空字段
        empty_fields = [
            'Date sale price starts', 'Date sale price ends', 'Weight (kg)', 'Length (cm)', 
            'Width (cm)', 'Height (cm)', 'Purchase note', 'Shipping class', 'Download limit', 
            'Download expiry days', 'Parent', 'Grouped products', 'Upsells', 'Cross-sells', 
            'External URL', 'Button text', 'Position', 'Attribute 4 name', 'Attribute 4 value(s)', 
            'Attribute 4 visible', 'Attribute 4 global', 'Meta: rank_math_focus_keyword'
        ]
        
        for field in empty_fields:
            wc_row[field] = ''
        
        return wc_row

    def process_file(self, input_file: Path, output_dir: Path) -> bool:
        """处理单个Excel文件"""
        try:
            logger.info(f"开始处理文件: {input_file.name}")

            # 读取Excel文件
            df = pd.read_excel(input_file)
            logger.info(f"读取到 {len(df)} 行数据")

            # 检测文件格式
            file_format = self.detect_file_format(df)
            logger.info(f"检测到文件格式: {file_format}")

            # 转换数据
            wc_rows = []
            for index, row in df.iterrows():
                if index % 100 == 0:
                    logger.info(f"已处理 {index} 行...")

                try:
                    wc_row = self.convert_row_to_wc(row.to_dict(), file_format)
                    wc_rows.append(wc_row)
                except Exception as e:
                    logger.warning(f"第 {index+1} 行转换失败: {e}")
                    continue

            if not wc_rows:
                logger.error("没有成功转换的数据")
                return False

            # 保存为CSV
            output_file = output_dir / f"{input_file.stem}_woocommerce.csv"
            wc_df = pd.DataFrame(wc_rows, columns=self.wc_columns)
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            logger.info(f"转换完成，保存到: {output_file}")
            logger.info(f"成功转换 {len(wc_rows)} 行数据")

            return True

        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return False

    def process_directory(self, input_dir: Path, output_dir: Path):
        """处理整个目录"""
        if not input_dir.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return

        # 创建输出目录
        output_dir.mkdir(exist_ok=True)

        # 获取所有Excel文件
        excel_files = list(input_dir.glob("*.xlsx"))
        excel_files = [f for f in excel_files if not f.name.startswith('~')]  # 排除临时文件

        if not excel_files:
            logger.error("没有找到Excel文件")
            return

        logger.info(f"找到 {len(excel_files)} 个Excel文件:")
        for file in excel_files:
            logger.info(f"  - {file.name}")

        # 处理每个文件
        success_count = 0
        for file in excel_files:
            logger.info(f"\n{'='*60}")
            logger.info(f"处理文件: {file.name}")
            logger.info(f"{'='*60}")

            if self.process_file(file, output_dir):
                success_count += 1
                logger.info(f"✓ {file.name} 处理成功")
            else:
                logger.error(f"✗ {file.name} 处理失败")

        logger.info(f"\n处理完成! 成功: {success_count}/{len(excel_files)}")
        logger.info(f"输出文件保存在: {output_dir.absolute()}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='德国源数据转换脚本')
    parser.add_argument('-i', '--input', default='源数据文件/de', help='输入目录路径')
    parser.add_argument('-o', '--output', default='woocommerce_output_de', help='输出目录路径')
    parser.add_argument('--test', action='store_true', help='测试模式，只处理前3个文件')

    args = parser.parse_args()

    # 创建转换器
    converter = DESourceConverter()

    input_path = Path(args.input)
    output_path = Path(args.output)

    if args.test:
        logger.info("测试模式：只处理前3个文件")
        # 获取前3个文件进行测试
        excel_files = list(input_path.glob("*.xlsx"))[:3]
        excel_files = [f for f in excel_files if not f.name.startswith('~')]

        output_path.mkdir(exist_ok=True)

        for file in excel_files:
            logger.info(f"测试处理: {file.name}")
            converter.process_file(file, output_path)
    else:
        # 处理整个目录
        converter.process_directory(input_path, output_path)

if __name__ == "__main__":
    main()
