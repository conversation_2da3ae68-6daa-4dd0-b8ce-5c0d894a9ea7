#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SKU生成问题的补丁脚本
"""

import re
from typing import Dict, List

class FixedSKUGenerator:
    def __init__(self, prefix: str = "DE"):
        self.sku_counter = 1
        self.sku_prefix = prefix
        self.used_skus = set()
        
        # 停用词列表
        self.stop_words = {
            'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an',
            'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'und', 'oder', 'mit'
        }

    def extract_keywords(self, text: str, max_words: int = 2) -> List[str]:
        """提取关键词"""
        if not text or str(text).strip() == 'nan':
            return []
        
        # 清理文本
        clean_text = re.sub(r'[^\w\s]', ' ', str(text).lower())
        words = clean_text.split()
        
        # 过滤停用词和短词
        keywords = []
        for word in words:
            if (len(word) > 2 and 
                word not in self.stop_words and 
                not word.isdigit() and
                len(keywords) < max_words):
                keywords.append(word)
        
        return keywords

    def generate_sku_improved(self, name: str, brand: str = "", original_id: str = "") -> str:
        """改进的SKU生成方法"""
        
        # 策略1: 如果有原始ID且格式良好，使用它
        if original_id and str(original_id).strip() != 'nan':
            clean_id = re.sub(r'[^\w\-]', '', str(original_id).strip())
            if len(clean_id) <= 12 and clean_id:
                sku = f"{self.sku_prefix}-{clean_id}"
                if sku not in self.used_skus:
                    self.used_skus.add(sku)
                    return sku[:20]
        
        # 策略2: 基于产品名称和品牌生成
        components = [self.sku_prefix]
        
        # 品牌部分
        if brand and str(brand).strip() != 'nan':
            brand_clean = re.sub(r'[^\w]', '', str(brand))[:4].upper()
            if brand_clean:
                components.append(brand_clean)
        
        # 产品关键词部分
        keywords = self.extract_keywords(name, 2)
        if keywords:
            product_part = ''.join([word[:4].upper() for word in keywords])
            components.append(product_part[:8])
        else:
            # 兜底：使用产品名称前几个字符
            clean_name = re.sub(r'[^\w]', '', str(name))[:6].upper()
            if clean_name:
                components.append(clean_name)
        
        # 序号部分
        components.append(f"{self.sku_counter:03d}")
        
        sku = '-'.join(components)
        
        # 长度控制
        if len(sku) > 20:
            if brand and str(brand).strip() != 'nan':
                brand_part = re.sub(r'[^\w]', '', str(brand))[:3].upper()
                name_part = re.sub(r'[^\w]', '', str(name))[:4].upper()
                sku = f"{self.sku_prefix}-{brand_part}-{name_part}-{self.sku_counter:03d}"
            else:
                name_part = re.sub(r'[^\w]', '', str(name))[:6].upper()
                sku = f"{self.sku_prefix}-{name_part}-{self.sku_counter:03d}"
        
        # 确保唯一性
        original_sku = sku
        counter = 1
        while sku in self.used_skus:
            sku = f"{original_sku[:-3]}{counter:03d}"
            counter += 1
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:20]

# 测试示例
if __name__ == "__main__":
    generator = FixedSKUGenerator("DE")
    
    test_cases = [
        ("LED Ceiling Light Modern Design", "Philips", "LAMP001"),
        ("Bauhaus Werkzeug Set Professional", "Bosch", ""),
        ("Gartenmöbel Set Outdoor Tisch", "", "GARD-2024-001"),
        ("Smart Home Beleuchtung WiFi", "Xiaomi", ""),
    ]
    
    print("=== 改进的SKU生成测试 ===")
    for name, brand, original_id in test_cases:
        sku = generator.generate_sku_improved(name, brand, original_id)
        print(f"产品: {name[:30]}...")
        print(f"品牌: {brand or 'N/A'}")
        print(f"原ID: {original_id or 'N/A'}")
        print(f"生成SKU: {sku}")
        print("-" * 50)
