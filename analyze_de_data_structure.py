#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from pathlib import Path
import re

def analyze_file_structure(file_path):
    """分析单个文件的数据结构"""
    try:
        print(f"\n{'='*60}")
        print(f"📁 分析文件: {file_path.name}")
        print('='*60)
        
        # 读取前10行数据
        df = pd.read_excel(file_path, nrows=10)
        
        print(f"📊 基本信息:")
        print(f"  总列数: {len(df.columns)}")
        print(f"  数据行数: {len(df)}")
        
        # 显示所有列名
        print(f"\n📋 所有列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 重点分析第一列
        first_col = df.iloc[:, 0]
        first_col_name = df.columns[0]
        
        print(f"\n🔍 第一列详细分析:")
        print(f"  列名: '{first_col_name}'")
        print(f"  数据类型: {first_col.dtype}")
        print(f"  唯一值数量: {first_col.nunique()}")
        print(f"  空值数量: {first_col.isnull().sum()}")
        
        print(f"\n📋 第一列样本数据:")
        for i in range(min(5, len(first_col))):
            value = first_col.iloc[i]
            print(f"  第{i+1}行: {value} (类型: {type(value).__name__})")
        
        # 分析第一列的特征
        print(f"\n💡 第一列特征分析:")
        
        # 检查列名特征
        col_lower = first_col_name.lower()
        name_indicators = ['id', 'sku', 'code', 'number', 'artikel', 'produkt']
        if any(indicator in col_lower for indicator in name_indicators):
            print(f"  ✅ 列名包含ID相关关键词: {[ind for ind in name_indicators if ind in col_lower]}")
        
        # 检查数据特征
        if first_col.nunique() == len(first_col):
            print(f"  ✅ 所有值都是唯一的 - 适合作为ID")
        else:
            print(f"  ⚠️  有重复值 - 可能不适合直接作为SKU")
        
        # 检查数据格式
        sample_values = [str(val) for val in first_col.head().tolist() if pd.notna(val)]
        if sample_values:
            # 检查是否是数字格式
            numeric_count = sum(1 for val in sample_values if val.isdigit())
            alphanumeric_count = sum(1 for val in sample_values if re.match(r'^[A-Za-z0-9\-_]+$', val))
            
            if numeric_count == len(sample_values):
                print(f"  📊 数据格式: 纯数字")
            elif alphanumeric_count == len(sample_values):
                print(f"  📊 数据格式: 字母数字组合")
            else:
                print(f"  📊 数据格式: 混合格式")
        
        # SKU适用性评估
        print(f"\n🎯 SKU生成策略建议:")
        
        score = 0
        reasons = []
        
        # 评分标准
        if any(indicator in col_lower for indicator in name_indicators):
            score += 3
            reasons.append("列名暗示这是ID字段")
        
        if first_col.nunique() == len(first_col):
            score += 3
            reasons.append("所有值唯一")
        
        if not first_col.isnull().any():
            score += 2
            reasons.append("无空值")
        
        if sample_values and all(len(str(val)) <= 20 for val in sample_values):
            score += 1
            reasons.append("长度适中")
        
        print(f"  评分: {score}/9")
        for reason in reasons:
            print(f"  • {reason}")
        
        if score >= 6:
            recommendation = "✅ 强烈推荐直接使用第一列作为SKU基础"
        elif score >= 4:
            recommendation = "⚠️  可以考虑使用第一列，但需要清理"
        else:
            recommendation = "❌ 不建议使用第一列，应该智能生成SKU"
        
        print(f"\n🎯 推荐策略: {recommendation}")
        
        return {
            'file_name': file_path.name,
            'first_col_name': first_col_name,
            'score': score,
            'recommendation': recommendation,
            'unique_values': first_col.nunique() == len(first_col),
            'has_nulls': first_col.isnull().any(),
            'sample_values': sample_values[:3]
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def main():
    """主函数"""
    # 德国数据文件路径
    de_data_path = Path("源数据文件/de")
    
    if not de_data_path.exists():
        print("❌ 德国数据目录不存在!")
        return
    
    # 获取所有Excel文件
    excel_files = list(de_data_path.glob("*.xlsx"))
    excel_files = [f for f in excel_files if not f.name.startswith('~')]  # 排除临时文件
    
    if not excel_files:
        print("❌ 没有找到Excel文件!")
        return
    
    print("🚀 德国电商数据结构分析")
    print(f"📂 数据目录: {de_data_path}")
    print(f"📊 找到 {len(excel_files)} 个Excel文件")
    
    # 分析所有文件
    analysis_results = []
    
    for i, file_path in enumerate(excel_files[:5], 1):  # 分析前5个文件
        print(f"\n[{i}/{min(5, len(excel_files))}] 正在分析...")
        result = analyze_file_structure(file_path)
        if result:
            analysis_results.append(result)
    
    # 生成汇总报告
    if analysis_results:
        print("\n" + "="*80)
        print("📈 汇总分析报告")
        print("="*80)
        
        print(f"\n📊 文件分析结果:")
        for result in analysis_results:
            print(f"  {result['file_name'][:30]:<30} | 第一列: {result['first_col_name'][:20]:<20} | 评分: {result['score']}/9")
        
        # 统计推荐策略
        high_score = [r for r in analysis_results if r['score'] >= 6]
        medium_score = [r for r in analysis_results if 4 <= r['score'] < 6]
        low_score = [r for r in analysis_results if r['score'] < 4]
        
        print(f"\n🎯 SKU策略分布:")
        print(f"  ✅ 直接使用第一列: {len(high_score)} 个文件")
        print(f"  ⚠️  需要清理处理: {len(medium_score)} 个文件")
        print(f"  ❌ 需要智能生成: {len(low_score)} 个文件")
        
        # 最终建议
        print(f"\n💡 最终SKU生成策略建议:")
        if len(high_score) >= len(analysis_results) // 2:
            print("  🎯 主策略: 优先使用第一列作为SKU基础")
            print("  📋 实现: 清理第一列数据 + DE前缀")
        else:
            print("  🎯 主策略: 智能SKU生成算法")
            print("  📋 实现: 品牌+关键词+序号组合")
        
        print("  🔄 备选策略: 多层次回退机制")
        print("    1. 尝试使用第一列")
        print("    2. 尝试使用其他ID字段")
        print("    3. 基于产品名称+品牌生成")
        print("    4. 哈希生成(兜底)")
    
    else:
        print("❌ 没有成功分析任何文件")

if __name__ == "__main__":
    main()
