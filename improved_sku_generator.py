#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的SKU生成器 - 结合原版讨论的高级方案
"""

import re
import hashlib
from typing import Dict, List, Any, Optional

class ImprovedSKUGenerator:
    def __init__(self, prefix: str = "DE"):
        self.sku_counter = 1
        self.sku_prefix = prefix
        self.used_skus = set()
        
        # 常见停用词
        self.stop_words = {
            'the', 'and', 'or', 'for', 'with', 'in', 'on', 'at', 'to', 'of', 'a', 'an',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
            'can', 'must', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 
            'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
        
        # 德语常见停用词
        self.german_stop_words = {
            'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'einen', 'einem',
            'einer', 'eines', 'und', 'oder', 'aber', 'mit', 'von', 'zu', 'auf', 'in',
            'an', 'bei', 'nach', 'vor', 'über', 'unter', 'durch', 'für', 'ohne',
            'gegen', 'um', 'bis', 'seit', 'während', 'wegen', 'trotz', 'statt'
        }
        
        self.all_stop_words = self.stop_words.union(self.german_stop_words)

    def extract_keywords(self, text: str, max_keywords: int = 3) -> List[str]:
        """提取关键词"""
        if not text or str(text).strip() == 'nan':
            return []
        
        # 清理文本
        clean_text = re.sub(r'[^\w\s\-]', ' ', str(text).lower())
        words = clean_text.split()
        
        # 过滤停用词和短词
        keywords = []
        for word in words:
            if (len(word) > 2 and 
                word not in self.all_stop_words and 
                not word.isdigit() and
                len(keywords) < max_keywords):
                keywords.append(word)
        
        return keywords

    def generate_sku_from_original(self, original_id: str, source_file: str = "") -> str:
        """基于原始ID生成SKU"""
        if not original_id or str(original_id).strip() == 'nan':
            return None
        
        clean_id = str(original_id).strip()
        
        # 如果原始ID已经很好，直接使用
        if len(clean_id) <= 15 and re.match(r'^[A-Za-z0-9\-_]+$', clean_id):
            sku = f"{self.sku_prefix}-{clean_id}"
        else:
            # 清理原始ID
            clean_id = re.sub(r'[^\w\-]', '', clean_id)[:10]
            sku = f"{self.sku_prefix}-{clean_id}-{self.sku_counter:03d}"
        
        # 确保唯一性
        if sku in self.used_skus:
            sku = f"{sku}-{self.sku_counter:03d}"
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:20]  # 限制长度

    def generate_sku_from_name_brand(self, product_name: str, brand: str = "", category: str = "") -> str:
        """基于产品名称和品牌生成SKU - 高级版本"""
        
        # 提取产品关键词
        name_keywords = self.extract_keywords(product_name, 2)
        
        # 构建SKU组件
        components = [self.sku_prefix]
        
        # 品牌部分
        if brand and str(brand).strip() != 'nan':
            brand_clean = re.sub(r'[^\w]', '', str(brand))[:4].upper()
            if brand_clean:
                components.append(brand_clean)
        
        # 产品关键词部分
        if name_keywords:
            product_part = ''.join([word[:4].upper() for word in name_keywords])
            components.append(product_part[:8])  # 限制长度
        else:
            # 如果没有关键词，使用产品名称的前几个字符
            clean_name = re.sub(r'[^\w]', '', str(product_name))[:6].upper()
            if clean_name:
                components.append(clean_name)
        
        # 分类部分（可选）
        if category and len('-'.join(components)) < 12:
            cat_clean = re.sub(r'[^\w]', '', str(category))[:3].upper()
            if cat_clean:
                components.append(cat_clean)
        
        # 序号部分
        components.append(f"{self.sku_counter:03d}")
        
        sku = '-'.join(components)
        
        # 确保长度限制
        if len(sku) > 20:
            # 简化版本
            if brand and str(brand).strip() != 'nan':
                brand_part = re.sub(r'[^\w]', '', str(brand))[:3].upper()
                name_part = re.sub(r'[^\w]', '', str(product_name))[:4].upper()
                sku = f"{self.sku_prefix}-{brand_part}-{name_part}-{self.sku_counter:03d}"
            else:
                name_part = re.sub(r'[^\w]', '', str(product_name))[:6].upper()
                sku = f"{self.sku_prefix}-{name_part}-{self.sku_counter:03d}"
        
        # 确保唯一性
        original_sku = sku
        counter = 1
        while sku in self.used_skus:
            sku = f"{original_sku[:-3]}{counter:03d}"
            counter += 1
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:20]

    def generate_sku_hash_based(self, product_name: str, brand: str = "", price: str = "") -> str:
        """基于哈希的SKU生成（确保唯一性）"""
        # 创建唯一标识符
        identifier = f"{product_name}|{brand}|{price}|{self.sku_counter}"
        hash_value = hashlib.md5(identifier.encode()).hexdigest()[:6].upper()
        
        sku = f"{self.sku_prefix}-{hash_value}"
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku

    def generate_sku_smart(self, row: Dict, source_file: str = "") -> str:
        """智能SKU生成 - 综合策略"""
        
        # 获取数据
        original_id = row.get('ID', row.get('id', row.get('sku', row.get('SKU', ''))))
        product_name = row.get('Name', row.get('name', row.get('title', row.get('post_title', ''))))
        brand = row.get('Brand', row.get('brand', row.get('attribute:Brand', '')))
        category = row.get('Categories', row.get('category', row.get('tax:product_cat', '')))
        price = row.get('Regular price', row.get('price', row.get('regular_price', '')))
        
        # 策略1: 使用原始ID（如果存在且合适）
        if original_id and str(original_id).strip() != 'nan':
            sku = self.generate_sku_from_original(original_id, source_file)
            if sku:
                return sku
        
        # 策略2: 基于产品名称和品牌生成
        if product_name and str(product_name).strip() != 'nan':
            return self.generate_sku_from_name_brand(product_name, brand, category)
        
        # 策略3: 基于哈希生成（兜底方案）
        return self.generate_sku_hash_based(
            str(product_name), str(brand), str(price)
        )

def test_sku_generator():
    """测试SKU生成器"""
    generator = ImprovedSKUGenerator("DE")
    
    test_cases = [
        {
            'ID': 'LAMP001',
            'Name': 'LED Ceiling Light Modern Design',
            'Brand': 'Philips',
            'Categories': 'Lighting > Ceiling Lights'
        },
        {
            'Name': 'Bauhaus Werkzeug Set Professional',
            'Brand': 'Bosch',
            'price': '199.99'
        },
        {
            'Name': 'Gartenmöbel Set Outdoor Tisch und Stühle',
            'Brand': '',
            'Categories': 'Garden > Furniture'
        }
    ]
    
    print("=== SKU生成测试 ===")
    for i, case in enumerate(test_cases, 1):
        sku = generator.generate_sku_smart(case)
        print(f"测试 {i}: {sku}")
        print(f"  产品: {case.get('Name', 'N/A')}")
        print(f"  品牌: {case.get('Brand', 'N/A')}")
        print()

if __name__ == "__main__":
    test_sku_generator()
