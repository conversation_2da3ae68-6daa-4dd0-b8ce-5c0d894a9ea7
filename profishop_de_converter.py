#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Profishop-DE 专用转换器 - 基于数据分析的优化版本
"""

import pandas as pd
import re
import logging
import random
from pathlib import Path
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProfishopDEConverter:
    def __init__(self):
        self.sku_counter = 1
        self.sku_prefix = "DE-PROF"
        self.used_skus = set()
        
        # Profishop-DE 字段映射 (基于实际分析结果)
        self.field_mapping = {
            'id': 'ID',                    # 第一列，数字ID
            'name': 'title',               # 产品名称
            'description': 'alldes',       # 主要描述字段 (对应 all-description l)
            'short_description': 'description s',  # 简短描述
            'price': 'price',              # 价格
            'sku_original': 'sku',         # 原始SKU
            'brand': 'Brand',              # 品牌
            'manufacturer': 'MFG',         # 制造商
            'category': 'category',        # 分类
            'image_primary': 'image',      # 主图片 (对应 image l)
            'image_secondary': 'S-IMANGE', # 备用图片 (对应 s-image l)
            'tags': 'tags',                # 标签
            'upc': 'UPC',                  # UPC码
            'page_url': 'PageUrl'          # 页面URL
        }
        
        # WooCommerce完整列定义
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global'
        ]

    def get_field_value(self, row: pd.Series, field_key: str) -> str:
        """获取字段值"""
        field_name = self.field_mapping.get(field_key)
        if field_name and field_name in row.index and pd.notna(row[field_name]):
            return str(row[field_name]).strip()
        return ''

    def generate_profishop_sku(self, row: pd.Series) -> str:
        """生成Profishop专用SKU"""
        
        # 策略1: 使用原始SKU (如果存在且合适)
        original_sku = self.get_field_value(row, 'sku_original')
        if original_sku and len(original_sku) <= 15:
            sku = f"{self.sku_prefix}-{original_sku}"
            if sku not in self.used_skus:
                self.used_skus.add(sku)
                return sku
        
        # 策略2: 使用ID (第一列，数字ID)
        product_id = self.get_field_value(row, 'id')
        if product_id:
            sku = f"{self.sku_prefix}-{product_id}"
            if sku not in self.used_skus:
                self.used_skus.add(sku)
                return sku
        
        # 策略3: 基于品牌和产品名称生成
        brand = self.get_field_value(row, 'brand')
        name = self.get_field_value(row, 'name')
        
        components = [self.sku_prefix]
        
        if brand:
            brand_clean = re.sub(r'[^\w]', '', brand)[:4].upper()
            components.append(brand_clean)
        
        if name:
            # 提取关键词
            clean_name = re.sub(r'[^\w\s]', ' ', name.lower())
            words = [w for w in clean_name.split() if len(w) > 2][:2]
            if words:
                name_part = ''.join([w[:3].upper() for w in words])
                components.append(name_part)
        
        components.append(f"{self.sku_counter:04d}")
        
        sku = '-'.join(components)
        
        # 确保唯一性
        while sku in self.used_skus:
            self.sku_counter += 1
            components[-1] = f"{self.sku_counter:04d}"
            sku = '-'.join(components)
        
        self.used_skus.add(sku)
        self.sku_counter += 1
        return sku[:25]  # 限制长度

    def clean_html_content(self, html_content: str) -> str:
        """清理HTML内容 - 集成原脚本的功能"""
        if not html_content or str(html_content).strip() == 'nan':
            return ""
        
        content = str(html_content)
        
        try:
            # 使用BeautifulSoup进行深度清理
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除危险标签
            for tag in soup(['script', 'style', 'iframe', 'object', 'embed']):
                tag.decompose()
            
            # 智能标签替换
            for h1 in soup.find_all(['h1', 'h2']):
                h1.name = 'h3'
            
            for font in soup.find_all('font'):
                font.name = 'span'
            
            for center in soup.find_all('center'):
                center.name = 'div'
                center['style'] = 'text-align: center;'
            
            # 移除事件属性
            for tag in soup.find_all():
                attrs_to_remove = [attr for attr in tag.attrs if attr.startswith('on')]
                for attr in attrs_to_remove:
                    del tag[attr]
            
            return str(soup)
            
        except:
            # 如果BeautifulSoup失败，使用正则表达式
            replacements = {
                r'<h[12]([^>]*)>': r'<h3\1>',
                r'</h[12]>': '</h3>',
                r'<font([^>]*)>': r'<span\1>',
                r'</font>': '</span>',
                r'<center([^>]*)>': r'<div style="text-align: center;"\1>',
                r'</center>': '</div>'
            }
            
            for pattern, replacement in replacements.items():
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            
            # 移除危险标签
            dangerous_tags = ['script', 'style', 'iframe', 'object', 'embed']
            for tag in dangerous_tags:
                content = re.sub(f'<{tag}[^>]*>.*?</{tag}>', '', content, flags=re.IGNORECASE | re.DOTALL)
            
            # 移除事件属性
            content = re.sub(r'\s*on\w+\s*=\s*["\'][^"\']*["\']', '', content, flags=re.IGNORECASE)
            
            return content.strip()

    def calculate_sale_price(self, regular_price: float) -> float:
        """计算促销价格 - 优化版本"""
        if regular_price <= 0:
            return 0.0
        
        # 特殊规则：价格小于10的不打折
        if regular_price < 10:
            return regular_price
        
        # 分层价格策略 (集成原脚本逻辑)
        if regular_price <= 50:
            discount_factor = random.uniform(0.4, 0.7)   # 30-60%折扣
        elif regular_price <= 200:
            discount_factor = random.uniform(0.6, 0.8)   # 20-40%折扣
        elif regular_price <= 500:
            discount_factor = random.uniform(0.75, 0.9)  # 10-25%折扣
        else:
            discount_factor = random.uniform(0.85, 0.95) # 5-15%折扣
        
        sale_price = regular_price * discount_factor
        return round(sale_price, 2)

    def process_images(self, row: pd.Series) -> str:
        """处理图片字段 - 实现替补逻辑"""
        # 优先使用主图片
        primary_image = self.get_field_value(row, 'image_primary')
        
        if primary_image and primary_image.strip():
            return primary_image.strip()
        
        # 主图片缺失时使用备用图片
        secondary_image = self.get_field_value(row, 'image_secondary')
        
        if secondary_image and secondary_image.strip():
            logger.info(f"使用备用图片替补: {secondary_image[:50]}...")
            return secondary_image.strip()
        
        return ""

    def process_categories(self, category_str: str) -> str:
        """处理分类字段 - 集成原脚本逻辑"""
        if not category_str or str(category_str).strip() == 'nan':
            return "Uncategorized"
        
        categories = str(category_str).strip()
        
        # 处理不同的分隔符
        if '>' in categories:
            parts = [part.strip() for part in categories.split('>') if part.strip()]
        elif '|||' in categories:
            parts = [part.strip() for part in categories.split('|||') if part.strip()]
        elif ',' in categories:
            parts = [part.strip() for part in categories.split(',') if part.strip()]
        else:
            parts = [categories]
        
        # 清理和去重
        cleaned_parts = []
        seen = set()
        for part in parts:
            if part and part.lower() not in seen:
                cleaned_parts.append(part)
                seen.add(part.lower())
        
        return ' > '.join(cleaned_parts) if cleaned_parts else "Uncategorized"

    def convert_row_to_wc(self, row: pd.Series) -> Dict:
        """将Profishop行数据转换为WooCommerce格式"""
        wc_row = {}
        
        # 基础字段
        wc_row['ID'] = ''
        wc_row['Type'] = 'simple'
        wc_row['SKU'] = self.generate_profishop_sku(row)
        
        # 产品信息
        wc_row['Name'] = self.get_field_value(row, 'name')
        
        # 描述字段 - 使用 alldes 作为主要描述
        main_description = self.get_field_value(row, 'description')  # alldes
        short_description = self.get_field_value(row, 'short_description')  # description s
        
        wc_row['Description'] = self.clean_html_content(main_description)
        wc_row['Short description'] = self.clean_html_content(short_description)
        
        # 价格处理
        price_str = self.get_field_value(row, 'price')
        try:
            regular_price = float(price_str) if price_str else 0.0
        except:
            regular_price = 0.0
        
        sale_price = self.calculate_sale_price(regular_price)
        
        wc_row['Regular price'] = regular_price
        wc_row['Sale price'] = sale_price
        
        # 分类和标签
        category = self.get_field_value(row, 'category')
        wc_row['Categories'] = self.process_categories(category)
        
        tags = self.get_field_value(row, 'tags')
        wc_row['Tags'] = tags if tags else wc_row['Categories'].split(' > ')[-1]
        
        # 图片处理 - 实现替补逻辑
        wc_row['Images'] = self.process_images(row)
        
        # 属性
        brand = self.get_field_value(row, 'brand')
        manufacturer = self.get_field_value(row, 'manufacturer')
        upc = self.get_field_value(row, 'upc')
        
        wc_row['Attribute 1 name'] = 'Brand'
        wc_row['Attribute 1 value(s)'] = brand
        wc_row['Attribute 1 visible'] = 1
        wc_row['Attribute 1 global'] = 0
        
        wc_row['Attribute 2 name'] = 'Manufacturer'
        wc_row['Attribute 2 value(s)'] = manufacturer
        wc_row['Attribute 2 visible'] = 1
        wc_row['Attribute 2 global'] = 0
        
        wc_row['Attribute 3 name'] = 'UPC'
        wc_row['Attribute 3 value(s)'] = upc
        wc_row['Attribute 3 visible'] = 0
        wc_row['Attribute 3 global'] = 0
        
        # 其他必需字段
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        wc_row['In stock?'] = 1
        wc_row['Stock'] = 999
        wc_row['Low stock amount'] = ''
        wc_row['Backorders allowed?'] = 0
        wc_row['Sold individually?'] = 0
        wc_row['Allow customer reviews?'] = 1
        wc_row['Purchase note'] = ''
        wc_row['Date sale price starts'] = ''
        wc_row['Date sale price ends'] = ''
        wc_row['Shipping class'] = ''
        
        # 其他空字段
        for col in self.wc_columns:
            if col not in wc_row:
                wc_row[col] = ''
        
        return wc_row

    def remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """去重处理 - 集成原脚本功能"""
        logger.info(f"去重前: {len(df)} 行")

        # 基于SKU去重，保留第一个
        df_unique = df.drop_duplicates(subset=['SKU'], keep='first')

        removed_count = len(df) - len(df_unique)
        if removed_count > 0:
            logger.info(f"移除重复SKU: {removed_count} 行")

        logger.info(f"去重后: {len(df_unique)} 行")
        return df_unique

    def process_profishop_file(self, input_file: Path) -> bool:
        """处理单个Profishop文件"""
        try:
            logger.info(f"开始处理Profishop文件: {input_file.name}")

            # 读取Excel文件
            df = pd.read_excel(input_file)
            logger.info(f"读取到 {len(df)} 行数据")

            # 验证字段
            missing_fields = []
            for key, field_name in self.field_mapping.items():
                if field_name not in df.columns:
                    missing_fields.append(f"{key} ({field_name})")

            if missing_fields:
                logger.warning(f"缺少字段: {', '.join(missing_fields)}")

            # 转换数据
            wc_rows = []
            image_substitutions = 0
            low_price_no_discount = 0

            for index, row in df.iterrows():
                if index % 1000 == 0 and index > 0:
                    logger.info(f"已处理 {index} 行...")

                wc_row = self.convert_row_to_wc(row)
                wc_rows.append(wc_row)

                # 统计信息
                if not self.get_field_value(row, 'image_primary') and self.get_field_value(row, 'image_secondary'):
                    image_substitutions += 1

                price = float(wc_row['Regular price']) if wc_row['Regular price'] else 0
                if 0 < price < 10 and wc_row['Sale price'] == wc_row['Regular price']:
                    low_price_no_discount += 1

            # 创建DataFrame
            wc_df = pd.DataFrame(wc_rows, columns=self.wc_columns)

            # 去重处理
            wc_df = self.remove_duplicates(wc_df)

            # 保存结果
            output_dir = Path("woocommerce_output_profishop")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / f"{input_file.stem}_woocommerce.csv"

            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            # 处理报告
            logger.info(f"转换完成，保存到: {output_file}")
            logger.info(f"成功转换 {len(wc_df)} 行数据")
            logger.info(f"图片替补次数: {image_substitutions}")
            logger.info(f"低价格无折扣商品: {low_price_no_discount}")

            return True

        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

def main():
    """主函数"""
    converter = ProfishopDEConverter()

    # Profishop数据文件路径
    source_dir = Path("源数据文件/de")

    if not source_dir.exists():
        logger.error("德国数据目录不存在!")
        return

    # 获取所有Profishop文件
    profishop_files = [f for f in source_dir.glob("profishop-de*.xlsx") if not f.name.startswith('~')]

    if not profishop_files:
        logger.error("没有找到profishop-de文件!")
        return

    logger.info(f"🚀 Profishop-DE 专用转换器启动")
    logger.info(f"📂 数据目录: {source_dir}")
    logger.info(f"📊 找到 {len(profishop_files)} 个profishop-de文件")

    # 显示优化特性
    logger.info(f"✨ 优化特性:")
    logger.info(f"  • 使用 'alldes' 作为主要描述字段")
    logger.info(f"  • 价格小于10的商品不打折")
    logger.info(f"  • 'image' 缺失时使用 'S-IMANGE' 替补")
    logger.info(f"  • 基于ID生成智能SKU")
    logger.info(f"  • 集成HTML清理和去重功能")

    # 处理所有文件
    success_count = 0
    total_products = 0

    for i, file in enumerate(profishop_files, 1):
        logger.info(f"\n[{i}/{len(profishop_files)}] 处理文件...")
        if converter.process_profishop_file(file):
            success_count += 1

            # 统计产品数量
            try:
                output_file = Path("woocommerce_output_profishop") / f"{file.stem}_woocommerce.csv"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8-sig') as f:
                        line_count = sum(1 for _ in f) - 1  # 减去标题行
                    total_products += line_count
            except:
                pass

    # 最终报告
    logger.info(f"\n" + "="*60)
    logger.info(f"🎉 Profishop-DE 转换完成!")
    logger.info(f"="*60)
    logger.info(f"成功处理: {success_count}/{len(profishop_files)} 个文件")
    logger.info(f"总产品数: {total_products:,} 个")
    logger.info(f"输出目录: woocommerce_output_profishop/")

    if success_count == len(profishop_files):
        logger.info(f"✅ 所有文件转换成功!")
    else:
        logger.warning(f"⚠️  部分文件转换失败，请检查日志")

if __name__ == "__main__":
    main()
