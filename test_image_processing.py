#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_woocommerce_converter import EnhancedWooCommerceConverter

def test_complex_image_processing():
    """测试复杂图片字段处理"""
    print("🧪 测试复杂图片字段处理...")
    print("="*80)
    
    converter = EnhancedWooCommerceConverter()
    
    # 测试用例
    test_cases = [
        {
            "name": "HTML标签混合格式",
            "input": 'https://media.cdn.bauhaus/m/1475267/12.webp" type="image/webp"><source srcset="https://media.cdn.bauhaus/m/1475267/12.jpg|https://media.cdn.bauhaus/m/1475267/12.jpg"|https://media.cdn.bauhaus/m/1474562/12.jpg"|https://media.cdn.bauhaus/m/1474286/12.jpg',
            "expected_count": 4
        },
        {
            "name": "重复图片URL",
            "input": "https://example.com/image1.jpg,https://example.com/image1.jpg,https://example.com/image2.png",
            "expected_count": 2
        },
        {
            "name": "多种分隔符",
            "input": "https://example.com/img1.jpg|||https://example.com/img2.png|https://example.com/img3.webp",
            "expected_count": 3
        },
        {
            "name": "带查询参数的URL",
            "input": "https://example.com/image.jpg?size=large,https://example.com/image.jpg?size=small,https://example.com/other.png",
            "expected_count": 2  # 同一图片不同尺寸应该去重
        },
        {
            "name": "HTML source标签",
            "input": '<source srcset="https://example.com/img1.webp" type="image/webp"><source srcset="https://example.com/img1.jpg" type="image/jpeg">',
            "expected_count": 2
        },
        {
            "name": "混合格式和无效URL",
            "input": "https://example.com/valid.jpg,invalid-url,https://example.com/another.png,not-an-image.txt",
            "expected_count": 2
        },
        {
            "name": "空字符串和null值",
            "input": "",
            "expected_count": 0
        },
        {
            "name": "单个有效URL",
            "input": "https://example.com/single-image.jpg",
            "expected_count": 1
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print(f"   输入: {test_case['input'][:100]}{'...' if len(test_case['input']) > 100 else ''}")
        
        result = converter.process_images(test_case['input'])
        result_urls = result.split(',') if result else []
        actual_count = len([url for url in result_urls if url.strip()])
        
        print(f"   输出: {result}")
        print(f"   期望数量: {test_case['expected_count']}, 实际数量: {actual_count}")
        
        if actual_count == test_case['expected_count']:
            print("   ✅ 测试通过")
        else:
            print("   ❌ 测试失败")
        
        # 显示提取的URL
        if result_urls:
            print("   提取的URL:")
            for j, url in enumerate(result_urls, 1):
                if url.strip():
                    print(f"     {j}. {url.strip()}")

def test_image_url_validation():
    """测试图片URL验证功能"""
    print("\n\n🔍 测试图片URL验证功能...")
    print("="*80)
    
    converter = EnhancedWooCommerceConverter()
    
    test_urls = [
        ("https://example.com/image.jpg", True, "标准JPG URL"),
        ("https://example.com/image.png", True, "标准PNG URL"),
        ("https://example.com/image.webp", True, "WebP格式"),
        ("https://example.com/image.gif", True, "GIF格式"),
        ("http://example.com/image.jpeg", True, "HTTP协议"),
        ("https://example.com/path/to/image.jpg?size=large", True, "带查询参数"),
        ("not-a-url", False, "无效URL格式"),
        ("https://example.com/document.pdf", False, "非图片文件"),
        ("ftp://example.com/image.jpg", False, "非HTTP协议"),
        ("", False, "空字符串"),
        ("https://", False, "不完整URL"),
        ("https://example.com/images/photo", False, "无扩展名"),
    ]
    
    for url, expected, description in test_urls:
        result = converter.is_valid_image_url(url)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}: {url} -> {result}")

def test_image_deduplication():
    """测试图片去重功能"""
    print("\n\n🔄 测试图片去重功能...")
    print("="*80)
    
    converter = EnhancedWooCommerceConverter()
    
    # 测试相同图片不同尺寸的去重
    urls_with_duplicates = [
        "https://example.com/image.jpg",
        "https://example.com/image_thumb.jpg",
        "https://example.com/image_large.jpg",
        "https://example.com/other.png",
        "https://example.com/image.jpg",  # 完全重复
        "https://example.com/another_small.png",
        "https://example.com/another.png",
    ]
    
    print("输入URL列表:")
    for i, url in enumerate(urls_with_duplicates, 1):
        print(f"  {i}. {url}")
    
    deduplicated = converter.deduplicate_and_validate_urls(urls_with_duplicates)
    
    print(f"\n去重后的URL列表 (共{len(deduplicated)}个):")
    for i, url in enumerate(deduplicated, 1):
        print(f"  {i}. {url}")

if __name__ == "__main__":
    test_complex_image_processing()
    test_image_url_validation()
    test_image_deduplication()
    
    print("\n" + "="*80)
    print("🎉 所有测试完成！")
