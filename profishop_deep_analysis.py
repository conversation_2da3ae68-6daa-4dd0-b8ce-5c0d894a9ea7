#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Profishop-DE 深度数据分析 - 发现更多优化机会
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
from collections import Counter
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def deep_analyze_profishop_data():
    """深度分析profishop数据"""
    
    source_dir = Path("源数据文件/de")
    profishop_files = [f for f in source_dir.glob("profishop-de*.xlsx") if not f.name.startswith('~')]
    
    if not profishop_files:
        print("❌ 没有找到profishop-de文件!")
        return
    
    print("🔬 Profishop-DE 深度数据分析")
    print("="*60)
    
    all_data = []
    
    # 读取所有数据
    for file in profishop_files[:2]:  # 分析前2个文件
        try:
            df = pd.read_excel(file, nrows=100)  # 每个文件读取100行样本
            df['source_file'] = file.name
            all_data.append(df)
            print(f"✅ 读取 {file.name}: {len(df)} 行")
        except Exception as e:
            print(f"❌ 读取 {file.name} 失败: {e}")
    
    if not all_data:
        print("❌ 没有成功读取任何数据")
        return
    
    # 合并数据
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"\n📊 合并数据: {len(combined_df)} 行, {len(combined_df.columns)} 列")
    
    # 1. 字段内容质量分析
    print(f"\n1️⃣ 字段内容质量分析")
    print("-" * 40)
    
    key_fields = ['title', 'description s', 'description', 'alldes', 'category', 'tags']
    
    for field in key_fields:
        if field in combined_df.columns:
            non_empty = combined_df[field].notna().sum()
            avg_length = combined_df[field].astype(str).str.len().mean()
            
            print(f"{field:15} | 非空: {non_empty:3d}/{len(combined_df)} ({non_empty/len(combined_df)*100:5.1f}%) | 平均长度: {avg_length:6.1f}")
            
            # 显示样本
            sample = combined_df[field].dropna().iloc[0] if non_empty > 0 else "N/A"
            print(f"{'':15} | 样本: {str(sample)[:60]}...")
    
    # 2. 品牌和制造商分析
    print(f"\n2️⃣ 品牌和制造商分析")
    print("-" * 40)
    
    if 'Brand' in combined_df.columns and 'MFG' in combined_df.columns:
        brands = combined_df['Brand'].value_counts().head(10)
        mfgs = combined_df['MFG'].value_counts().head(10)
        
        print("🏷️ 前10品牌:")
        for brand, count in brands.items():
            print(f"  {brand}: {count} 个产品")
        
        print("\n🏭 前10制造商:")
        for mfg, count in mfgs.items():
            print(f"  {mfg}: {count} 个产品")
        
        # 品牌与制造商一致性分析
        brand_mfg_same = (combined_df['Brand'] == combined_df['MFG']).sum()
        print(f"\n🔍 品牌与制造商一致: {brand_mfg_same}/{len(combined_df)} ({brand_mfg_same/len(combined_df)*100:.1f}%)")
    
    # 3. 价格分布深度分析
    print(f"\n3️⃣ 价格分布深度分析")
    print("-" * 40)
    
    if 'price' in combined_df.columns:
        prices = pd.to_numeric(combined_df['price'], errors='coerce').dropna()
        
        print(f"价格统计:")
        print(f"  总数: {len(prices)}")
        print(f"  范围: {prices.min():.2f} - {prices.max():.2f}")
        print(f"  平均: {prices.mean():.2f}")
        print(f"  中位数: {prices.median():.2f}")
        
        # 价格区间分布
        price_ranges = [
            (0, 10, "超低价"),
            (10, 50, "低价"),
            (50, 200, "中价"),
            (200, 500, "高价"),
            (500, 1000, "奢侈"),
            (1000, float('inf'), "超奢侈")
        ]
        
        print(f"\n💰 价格区间分布:")
        for min_p, max_p, label in price_ranges:
            count = ((prices >= min_p) & (prices < max_p)).sum()
            percentage = count / len(prices) * 100
            print(f"  {label:8} ({min_p:4.0f}-{max_p if max_p != float('inf') else '∞':>4}): {count:3d} ({percentage:5.1f}%)")
    
    # 4. 图片字段分析
    print(f"\n4️⃣ 图片字段分析")
    print("-" * 40)
    
    image_fields = ['image', 'S-IMANGE']
    
    for field in image_fields:
        if field in combined_df.columns:
            has_content = (combined_df[field].notna() & (combined_df[field] != '')).sum()
            print(f"{field:10} | 有内容: {has_content:3d}/{len(combined_df)} ({has_content/len(combined_df)*100:5.1f}%)")
            
            # 分析URL格式
            if has_content > 0:
                urls = combined_df[field].dropna()
                url_patterns = []
                for url in urls.head(5):
                    if 'http' in str(url):
                        url_patterns.append("HTTP链接")
                    elif str(url).endswith(('.jpg', '.png', '.gif')):
                        url_patterns.append("图片文件")
                    else:
                        url_patterns.append("其他格式")
                
                pattern_counts = Counter(url_patterns)
                print(f"{'':10} | 格式: {dict(pattern_counts)}")
    
    # 图片替补需求分析
    if 'image' in combined_df.columns and 'S-IMANGE' in combined_df.columns:
        primary_empty = (combined_df['image'].isna() | (combined_df['image'] == ''))
        secondary_available = (combined_df['S-IMANGE'].notna() & (combined_df['S-IMANGE'] != ''))
        
        substitution_needed = (primary_empty & secondary_available).sum()
        print(f"\n🔄 图片替补需求: {substitution_needed} 个产品需要使用备用图片")
    
    # 5. 分类和标签分析
    print(f"\n5️⃣ 分类和标签分析")
    print("-" * 40)
    
    if 'category' in combined_df.columns:
        categories = combined_df['category'].value_counts().head(10)
        print("📂 前10分类:")
        for cat, count in categories.items():
            print(f"  {str(cat)[:40]:40} | {count} 个产品")
    
    if 'tags' in combined_df.columns:
        # 分析标签模式
        all_tags = []
        for tags_str in combined_df['tags'].dropna():
            if ',' in str(tags_str):
                tags = [tag.strip() for tag in str(tags_str).split(',')]
                all_tags.extend(tags)
        
        if all_tags:
            tag_counts = Counter(all_tags)
            print(f"\n🏷️ 前10标签:")
            for tag, count in tag_counts.most_common(10):
                print(f"  {tag:20} | {count} 次")
    
    # 6. SKU和ID分析
    print(f"\n6️⃣ SKU和ID分析")
    print("-" * 40)
    
    if 'ID' in combined_df.columns:
        ids = combined_df['ID']
        print(f"ID字段:")
        print(f"  类型: {ids.dtype}")
        print(f"  唯一性: {ids.nunique()}/{len(ids)} ({ids.nunique()/len(ids)*100:.1f}%)")
        print(f"  范围: {ids.min()} - {ids.max()}")
    
    if 'sku' in combined_df.columns:
        skus = combined_df['sku'].dropna()
        print(f"\nSKU字段:")
        print(f"  非空: {len(skus)}/{len(combined_df)} ({len(skus)/len(combined_df)*100:.1f}%)")
        print(f"  唯一性: {skus.nunique()}/{len(skus)} ({skus.nunique()/len(skus)*100:.1f}%)")
        
        # SKU格式分析
        sku_patterns = []
        for sku in skus.head(10):
            sku_str = str(sku)
            if sku_str.isdigit():
                sku_patterns.append("纯数字")
            elif re.match(r'^[A-Z]+\d+$', sku_str):
                sku_patterns.append("字母+数字")
            elif '-' in sku_str:
                sku_patterns.append("包含连字符")
            else:
                sku_patterns.append("其他格式")
        
        pattern_counts = Counter(sku_patterns)
        print(f"  格式分布: {dict(pattern_counts)}")
    
    # 7. 数据优化建议
    print(f"\n7️⃣ 数据优化建议")
    print("-" * 40)
    
    suggestions = []
    
    # 描述字段优化
    if 'alldes' in combined_df.columns and 'description' in combined_df.columns:
        alldes_avg = combined_df['alldes'].astype(str).str.len().mean()
        desc_avg = combined_df['description'].astype(str).str.len().mean()
        
        if alldes_avg > desc_avg * 2:
            suggestions.append("✅ 'alldes' 内容更丰富，建议作为主描述")
        
        # 检查HTML内容
        html_in_alldes = combined_df['alldes'].astype(str).str.contains('<[^>]+>', regex=True).sum()
        if html_in_alldes > len(combined_df) * 0.5:
            suggestions.append("🧹 'alldes' 包含大量HTML，需要智能清理")
    
    # 图片优化
    if 'image' in combined_df.columns and 'S-IMANGE' in combined_df.columns:
        primary_empty = (combined_df['image'].isna() | (combined_df['image'] == '')).sum()
        if primary_empty > 0:
            suggestions.append(f"🖼️ {primary_empty} 个产品需要图片替补功能")
    
    # 价格优化
    if 'price' in combined_df.columns:
        low_prices = (pd.to_numeric(combined_df['price'], errors='coerce') < 10).sum()
        if low_prices > 0:
            suggestions.append(f"💰 {low_prices} 个低价产品建议不打折")
        else:
            suggestions.append("💰 无低价产品，可以应用正常折扣策略")
    
    # 品牌优化
    if 'Brand' in combined_df.columns and 'MFG' in combined_df.columns:
        brand_mfg_same = (combined_df['Brand'] == combined_df['MFG']).sum()
        if brand_mfg_same > len(combined_df) * 0.8:
            suggestions.append("🏷️ 品牌与制造商高度重复，可以简化处理")
    
    # SKU优化
    if 'ID' in combined_df.columns:
        id_unique = combined_df['ID'].nunique() == len(combined_df)
        if id_unique:
            suggestions.append("🏷️ ID字段完全唯一，非常适合作为SKU基础")
    
    print("💡 优化建议:")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")
    
    return combined_df

if __name__ == "__main__":
    deep_analyze_profishop_data()
