#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
域名检查脚本 - 检查域名可用性和相关信息
"""

import pandas as pd
import requests
import socket
import re
from pathlib import Path
from urllib.parse import urlparse
import time
import json

class DomainChecker:
    def __init__(self):
        self.common_tlds = ['.com', '.net', '.org', '.info', '.biz', '.co', '.io', '.shop', '.store']
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def is_valid_domain(self, domain: str):
        """检查域名格式是否有效"""
        if not domain or len(domain) < 4:
            return False
        
        # 基本格式检查
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        
        if not re.match(domain_pattern, domain):
            return False
        
        # 检查是否包含TLD
        if '.' not in domain:
            return False
        
        parts = domain.split('.')
        if len(parts) < 2:
            return False
        
        # 检查TLD长度
        tld = parts[-1]
        if len(tld) < 2 or len(tld) > 6:
            return False
        
        return True

    def check_dns_resolution(self, domain: str):
        """检查域名DNS解析"""
        try:
            socket.gethostbyname(domain)
            return True
        except socket.gaierror:
            return False

    def check_http_response(self, domain: str):
        """检查HTTP响应"""
        results = {
            'http_accessible': False,
            'https_accessible': False,
            'status_code': None,
            'redirect_url': None,
            'response_time': None
        }
        
        # 检查HTTPS
        try:
            start_time = time.time()
            response = self.session.get(f'https://{domain}', timeout=10, allow_redirects=True)
            response_time = time.time() - start_time
            
            results['https_accessible'] = True
            results['status_code'] = response.status_code
            results['response_time'] = round(response_time, 2)
            
            if response.url != f'https://{domain}':
                results['redirect_url'] = response.url
                
        except Exception as e:
            # 如果HTTPS失败，尝试HTTP
            try:
                start_time = time.time()
                response = self.session.get(f'http://{domain}', timeout=10, allow_redirects=True)
                response_time = time.time() - start_time
                
                results['http_accessible'] = True
                results['status_code'] = response.status_code
                results['response_time'] = round(response_time, 2)
                
                if response.url != f'http://{domain}':
                    results['redirect_url'] = response.url
                    
            except Exception:
                pass
        
        return results

    def analyze_domain_name(self, domain: str):
        """分析域名特征"""
        if not domain:
            return {}
        
        # 移除协议和路径
        if '://' in domain:
            domain = urlparse(domain).netloc
        
        domain = domain.lower().strip()
        
        # 分离域名和TLD
        parts = domain.split('.')
        if len(parts) < 2:
            return {'error': 'Invalid domain format'}
        
        domain_name = '.'.join(parts[:-1])
        tld = '.' + parts[-1]
        
        analysis = {
            'full_domain': domain,
            'domain_name': domain_name,
            'tld': tld,
            'length': len(domain_name),
            'has_numbers': bool(re.search(r'\d', domain_name)),
            'has_hyphens': '-' in domain_name,
            'word_count': len(re.findall(r'[a-zA-Z]+', domain_name)),
            'is_brandable': self.is_brandable(domain_name),
            'memorability_score': self.calculate_memorability_score(domain_name)
        }
        
        return analysis

    def is_brandable(self, domain_name: str):
        """判断域名是否适合品牌化"""
        # 简单的品牌化评估
        if len(domain_name) <= 8 and not '-' in domain_name and not any(char.isdigit() for char in domain_name):
            return True
        return False

    def calculate_memorability_score(self, domain_name: str):
        """计算域名记忆度评分 (1-10)"""
        score = 10
        
        # 长度惩罚
        if len(domain_name) > 12:
            score -= 3
        elif len(domain_name) > 8:
            score -= 1
        
        # 连字符惩罚
        if '-' in domain_name:
            score -= 2
        
        # 数字惩罚
        if any(char.isdigit() for char in domain_name):
            score -= 1
        
        # 复杂度惩罚
        if len(set(domain_name)) / len(domain_name) < 0.5:  # 重复字符多
            score -= 1
        
        return max(1, score)

    def check_single_domain(self, domain: str):
        """检查单个域名"""
        print(f"🔍 检查域名: {domain}")
        
        # 验证域名格式
        if not self.is_valid_domain(domain):
            return {
                'domain': domain,
                'valid': False,
                'error': 'Invalid domain format'
            }
        
        result = {
            'domain': domain,
            'valid': True,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 域名分析
        analysis = self.analyze_domain_name(domain)
        result.update(analysis)
        
        # DNS检查
        print(f"  📡 检查DNS解析...")
        result['dns_resolves'] = self.check_dns_resolution(domain)
        
        # HTTP检查
        if result['dns_resolves']:
            print(f"  🌐 检查HTTP访问...")
            http_results = self.check_http_response(domain)
            result.update(http_results)
            
            # 网站状态
            if result['https_accessible'] or result['http_accessible']:
                result['website_active'] = True
                result['availability_status'] = 'Taken - Active Website'
            else:
                result['website_active'] = False
                result['availability_status'] = 'Taken - No Website'
        else:
            result['website_active'] = False
            result['availability_status'] = 'Potentially Available'
        
        # 输出结果
        print(f"  ✅ DNS解析: {'是' if result['dns_resolves'] else '否'}")
        print(f"  🌐 网站活跃: {'是' if result.get('website_active', False) else '否'}")
        print(f"  📊 可用状态: {result['availability_status']}")
        print(f"  🎯 记忆度评分: {result.get('memorability_score', 'N/A')}/10")
        
        return result

    def check_domain_list(self, domains: list):
        """批量检查域名列表"""
        print(f"🚀 批量检查 {len(domains)} 个域名")
        print("="*60)
        
        results = []
        
        for i, domain in enumerate(domains, 1):
            print(f"\n[{i}/{len(domains)}]")
            
            try:
                result = self.check_single_domain(domain.strip())
                results.append(result)
                
                # 避免请求过于频繁
                if i < len(domains):
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ 检查失败: {e}")
                results.append({
                    'domain': domain,
                    'valid': False,
                    'error': str(e)
                })
        
        return results

    def check_domains_from_file(self, input_file: str, output_file: str = None):
        """从文件读取域名并检查"""
        try:
            print(f"📁 从文件读取域名: {Path(input_file).name}")
            
            # 读取域名列表
            if input_file.endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
                
                # 查找域名列
                domain_column = None
                for col in df.columns:
                    col_lower = str(col).lower()
                    if 'domain' in col_lower:
                        domain_column = col
                        break
                
                if domain_column is None:
                    # 使用第一列
                    domain_column = df.columns[0]
                
                domains = df[domain_column].dropna().astype(str).tolist()
                
            else:
                # 文本文件，每行一个域名
                with open(input_file, 'r', encoding='utf-8') as f:
                    domains = [line.strip() for line in f if line.strip()]
            
            print(f"📊 读取到 {len(domains)} 个域名")
            
            # 批量检查
            results = self.check_domain_list(domains)
            
            # 保存结果
            if output_file is None:
                input_path = Path(input_file)
                output_file = f"domain_check_results_{input_path.stem}.csv"
            
            df_results = pd.DataFrame(results)
            df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            # 生成统计报告
            self.generate_summary_report(results)
            
            print(f"\n📁 结果已保存: {output_file}")
            
            return output_file
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def generate_summary_report(self, results: list):
        """生成汇总报告"""
        print(f"\n📊 域名检查汇总报告")
        print("="*50)
        
        valid_results = [r for r in results if r.get('valid', False)]
        
        if not valid_results:
            print("❌ 没有有效的检查结果")
            return
        
        total = len(valid_results)
        dns_resolves = sum(1 for r in valid_results if r.get('dns_resolves', False))
        active_websites = sum(1 for r in valid_results if r.get('website_active', False))
        potentially_available = sum(1 for r in valid_results if r.get('availability_status') == 'Potentially Available')
        
        print(f"总域名数: {total}")
        print(f"DNS解析: {dns_resolves} ({dns_resolves/total*100:.1f}%)")
        print(f"活跃网站: {active_websites} ({active_websites/total*100:.1f}%)")
        print(f"可能可用: {potentially_available} ({potentially_available/total*100:.1f}%)")
        
        # 按可用状态分组
        status_counts = {}
        for result in valid_results:
            status = result.get('availability_status', 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"\n📋 可用状态分布:")
        for status, count in status_counts.items():
            print(f"  • {status}: {count} 个域名")
        
        # 记忆度评分分布
        scores = [r.get('memorability_score', 0) for r in valid_results if r.get('memorability_score')]
        if scores:
            avg_score = sum(scores) / len(scores)
            print(f"\n🎯 平均记忆度评分: {avg_score:.1f}/10")
            
            high_score_domains = [r for r in valid_results if r.get('memorability_score', 0) >= 8]
            if high_score_domains:
                print(f"高分域名 (≥8分): {len(high_score_domains)} 个")
                for domain_info in high_score_domains[:5]:  # 显示前5个
                    print(f"  • {domain_info['domain']} (评分: {domain_info['memorability_score']})")

    def suggest_alternatives(self, base_domain: str, count: int = 10):
        """为已占用的域名建议替代方案"""
        if '.' not in base_domain:
            return []
        
        domain_parts = base_domain.split('.')
        name = domain_parts[0]
        tld = '.' + domain_parts[1]
        
        alternatives = []
        
        # 不同TLD
        for alt_tld in self.common_tlds:
            if alt_tld != tld:
                alternatives.append(f"{name}{alt_tld}")
        
        # 添加前缀/后缀
        prefixes = ['get', 'my', 'the', 'best', 'top', 'pro']
        suffixes = ['hub', 'zone', 'pro', 'plus', 'online', 'store']
        
        for prefix in prefixes[:3]:
            alternatives.append(f"{prefix}{name}{tld}")
        
        for suffix in suffixes[:3]:
            alternatives.append(f"{name}{suffix}{tld}")
        
        return alternatives[:count]

def main():
    """主函数"""
    checker = DomainChecker()
    
    print("🔍 域名检查工具")
    print("="*40)
    
    print("📋 使用方法:")
    print("1. 检查单个域名:")
    print("   checker.check_single_domain('example.com')")
    print("2. 从文件批量检查:")
    print("   checker.check_domains_from_file('domains.csv')")
    print("3. 检查域名列表:")
    print("   checker.check_domain_list(['domain1.com', 'domain2.com'])")

if __name__ == "__main__":
    main()
