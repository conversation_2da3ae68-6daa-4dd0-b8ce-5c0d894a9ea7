import csv
import os
import re

# 尝试导入namecheap，如果失败则提供安装指导
try:
    from namecheap import Api
    NAMECHEAP_AVAILABLE = True
except ImportError:
    print("错误: 缺少 namecheap 模块")
    print("请运行以下命令安装:")
    print("pip install namecheap")
    print("或者:")
    print("python -m pip install namecheap")
    NAMECHEAP_AVAILABLE = False

# 检查模块是否可用
if not NAMECHEAP_AVAILABLE:
    print("由于缺少必要的模块，脚本无法运行。")
    print("请先安装所需的依赖包。")
    exit(1)

# --- 在这里配置您的信息 ---
API_KEY = 'f7f108c1bfbe4689a8d7ec0fcfe782e1'
USERNAME = 'virtuallord407'
CLIENT_IP = '**************'
API_USERNAME = 'virtuallord407'
# --- 配置结束 ---

def clean_text_for_domain(text):
    """清理文本，使其适合作为域名"""
    cleaned = re.sub(r'[^a-zA-Z0-9\s]', '', text)
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    words = cleaned.split()
    if len(words) == 1:
        return words[0].title()
    else:
        return ''.join(word.title() for word in words)

def generate_domain_from_category(primary_category, sub_categories):
    """根据分类信息生成域名"""
    domains = []
    
    # 基于一级分类生成域名
    primary_clean = clean_text_for_domain(primary_category)
    if primary_clean:
        domains.extend([
            f"{primary_clean}.com",
            f"{primary_clean}.net",
            f"{primary_clean}.co",
            f"{primary_clean}.store"
        ])
    
    # 基于二级分类生成域名
    for sub_cat, count in sub_categories:
        if count > 1000:  # 只对数量较多的二级分类生成域名
            sub_clean = clean_text_for_domain(sub_cat)
            if sub_clean and len(sub_clean) > 3:
                domains.extend([
                    f"{sub_clean}.com",
                    f"{sub_clean}.net"
                ])
    
    return domains

def read_categories_from_csv(csv_file='category_analysis_result.csv'):
    """从analyze_categories.py的输出结果中读取分类信息"""
    domains_to_check = []
    
    if not os.path.exists(csv_file):
        print(f"错误：找不到文件 {csv_file}")
        print("请先运行 analyze_categories.py 生成分类分析结果")
        return domains_to_check
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                filename = row['文件名']
                primary_category = row['一级分类']
                primary_count = int(row['一级分类数量'])
                
                # 解析二级分类
                sub_categories = []
                for i in range(1, 6):
                    sub_cat = row[f'二级分类{i}']
                    sub_count_str = row[f'二级分类{i}数量']
                    
                    if sub_cat and sub_count_str:
                        try:
                            sub_count = int(sub_count_str)
                            sub_categories.append((sub_cat, sub_count))
                        except ValueError:
                            continue
                
                # 生成域名
                generated_domains = generate_domain_from_category(primary_category, sub_categories)
                
                print(f"文件: {filename}")
                print(f"  一级分类: {primary_category} ({primary_count})")
                print(f"  生成域名: {generated_domains[:3]}...")  # 只显示前3个
                
                domains_to_check.extend(generated_domains)
        
        # 去重并限制数量
        domains_to_check = list(set(domains_to_check))[:50]  # 限制最多50个域名
        
        print(f"\n总共生成 {len(domains_to_check)} 个唯一域名")
        return domains_to_check
        
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return []

def check_domain_availability():
    """使用 Namecheap API 检查域名列表的可用性"""
    # 从CSV文件读取分类信息并生成域名
    print("正在从分类分析结果生成域名...")
    domains_to_check = read_categories_from_csv()
    
    if not domains_to_check:
        print("没有生成任何域名，请检查分类分析结果文件")
        return

    print("正在连接 Namecheap API 并开始查询...")

    try:
        # 初始化 API 客户端
        api = Api(
            api_user=API_USERNAME,
            api_key=API_KEY,
            username=USERNAME,
            client_ip=CLIENT_IP,
            sandbox=False,
            debug=False
        )

        # 将域名列表分成小块
        chunk_size = 30
        available_domains = []
        
        for i in range(0, len(domains_to_check), chunk_size):
            chunk = domains_to_check[i:i+chunk_size]
            print(f"\n--- 正在查询批次 {i//chunk_size + 1} ---")
            
            try:
                results = api.domains_check(chunk)
                
                # 遍历返回的结果并打印
                for domain, is_available in results.items():
                    if is_available:
                        print(f"{domain:<30} ✅ 可注册 (Available)")
                        available_domains.append(domain)
                    else:
                        print(f"{domain:<30} ❌ 已被注册 (Taken)")

            except Exception as e:
                print(f"查询批次时发生错误: {e}")
                print("这可能是因为域名格式无效或API速率限制。")
        
        # 保存可用域名到文件
        if available_domains:
            with open('available_domains.txt', 'w', encoding='utf-8') as f:
                for domain in available_domains:
                    f.write(f"{domain}\n")
            print(f"\n✅ 找到 {len(available_domains)} 个可用域名，已保存到 available_domains.txt")
        else:
            print("\n❌ 没有找到可用的域名")

    except Exception as e:
        print(f"连接到 Namecheap API 时发生严重错误: {e}")
        print("请检查您的 API 凭据和 IP 白名单是否正确。")

if __name__ == "__main__":
    check_domain_availability()