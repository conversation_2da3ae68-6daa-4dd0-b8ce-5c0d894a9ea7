﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WooCommerce产品数据分割工具
根据产品分类列中的一级分类对数据进行分割和重新组织
"""

import csv
import os
import re
from collections import defaultdict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 小分类合并阈值 - 产品数量少于此值的分类将被合并
SMALL_CATEGORY_THRESHOLD = 1000

def open_csv_with_encoding(filepath):
    """尝试多种编码打开CSV文件"""
    encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252']
    for enc in encodings:
        try:
            print(f"尝试使用编码: {enc}")
            return open(filepath, 'r', encoding=enc, errors='replace')
        except UnicodeDecodeError as e:
            print(f"编码 {enc} 失败: {e}")
            continue
    
    # 如果所有编码都失败，尝试二进制模式读取并修复
    print("尝试二进制模式读取并修复文件...")
    try:
        with open(filepath, 'rb') as f:
            content = f.read()
        
        print(f"文件大小: {len(content)} 字节")
        
        # 检测问题字节
        problematic_bytes = {}
        for i, byte in enumerate(content):
            if byte in [0xa8, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf]:
                if byte not in problematic_bytes:
                    problematic_bytes[byte] = []
                problematic_bytes[byte].append(i)
        
        if problematic_bytes:
            print("检测到问题字节:")
            for byte, positions in problematic_bytes.items():
                print(f"  0x{byte:02x} (位置: {positions[:5]}{'...' if len(positions) > 5 else ''})")
        
        # 尝试修复常见的编码问题
        original_size = len(content)
        
        # 移除null字节
        content = content.replace(b'\x00', b'')
        
        # 移除BOM标记
        content = content.replace(b'\xff\xfe', b'')  # UTF-16 LE BOM
        content = content.replace(b'\xfe\xff', b'')  # UTF-16 BE BOM
        content = content.replace(b'\xef\xbb\xbf', b'')  # UTF-8 BOM
        
        # 处理常见的GBK字符问题
        gbk_replacements = {
            b'\xa8': b' ',  # ¨ -> 空格
            b'\xa1': b'!',  # ¡ -> !
            b'\xa2': b'c',  # ¢ -> c
            b'\xa3': b'L',  # £ -> L
            b'\xa4': b'o',  # ¤ -> o
            b'\xa5': b'Y',  # ¥ -> Y
            b'\xa6': b'|',  # ¦ -> |
            b'\xa7': b'S',  # § -> S
            b'\xa9': b'(C)', # © -> (C)
            b'\xaa': b'a',  # ª -> a
            b'\xab': b'<<', # « -> <<
            b'\xac': b'~',  # ¬ -> ~
            b'\xad': b'-',  # ­ -> -
            b'\xae': b'(R)', # ® -> (R)
            b'\xaf': b'-',  # ¯ -> -
        }
        
        for old_byte, new_bytes in gbk_replacements.items():
            content = content.replace(old_byte, new_bytes)
        
        # 移除其他扩展ASCII字符
        for i in range(0x80, 0xa0):
            content = content.replace(bytes([i]), b' ')
        
        print(f"修复前大小: {original_size} 字节")
        print(f"修复后大小: {len(content)} 字节")
        
        # 尝试用utf-8解码，忽略错误
        text = content.decode('utf-8', errors='replace')
        
        # 创建临时文件
        temp_file = filepath + '.temp'
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(text)
        
        print(f"文件已修复并保存为临时文件: {temp_file}")
        return open(temp_file, 'r', encoding='utf-8')
        
    except Exception as e:
        print(f"文件修复失败: {e}")
        raise UnicodeDecodeError(f"无法识别文件编码: {filepath}")

def extract_primary_category(category_string):
    """提取一级分类名称"""
    if not category_string or category_string.strip() == '':
        return "未分类"
    
    category_string = category_string.strip()
    
    if ' > ' in category_string:
        primary = category_string.split(' > ')[0].strip()
    elif ',' in category_string:
        primary = category_string.split(',')[0].strip()
    else:
        primary = category_string
    
    # 清理文件名不安全字符
    primary = re.sub(r'[<>:"/\\|?*]', '_', primary)
    primary = primary.strip()
    
    return primary if primary else "未分类"

def process_all_files(input_files, max_rows_per_file=30000):
    """处理所有CSV文件并合并相同分类"""
    print("开始处理所有文件并合并相同分类...")
    print(f"小分类合并阈值: {SMALL_CATEGORY_THRESHOLD} 个产品")
    
    # 全局按分类组织数据
    global_category_data = defaultdict(list)
    global_headers = None
    total_rows = 0
    
    for input_file in input_files:
        input_path = os.path.join("input", input_file)
        print(f"处理文件: {input_path}")
        
        if not os.path.exists(input_path):
            print(f"文件不存在: {input_path}")
            continue
        
        try:
            print(f"正在打开文件: {input_file}")
            with open_csv_with_encoding(input_path) as csvfile:
                reader = csv.reader(csvfile)
                headers = next(reader)
                
                # 保存第一个文件的headers
                if global_headers is None:
                    global_headers = headers
                
                # 查找Categories列
                categories_index = None
                for i, header in enumerate(headers):
                    if header.lower() in ['categories', 'category', '产品分类']:
                        categories_index = i
                        break
                
                if categories_index is None:
                    print(f"文件 {input_file} 中未找到Categories列")
                    continue
                
                print(f"找到Categories列，索引: {categories_index}")
                
                # 处理每一行数据
                file_rows = 0
                for row_num, row in enumerate(reader, start=2):
                    try:
                        if len(row) <= categories_index:
                            continue
                        
                        file_rows += 1
                        total_rows += 1
                        category_string = row[categories_index]
                        primary_category = extract_primary_category(category_string)
                        
                        # 将行数据添加到全局分类中
                        global_category_data[primary_category].append(row)
                        
                        if row_num % 10000 == 0:
                            print(f"已处理 {row_num} 行数据")
                    except Exception as e:
                        print(f"跳过第 {row_num} 行（数据错误）: {e}")
                        continue
                
                print(f"文件 {input_file} 处理完成，共 {file_rows} 行数据")
                
        except Exception as e:
            print(f"处理文件 {input_file} 时出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n所有文件处理完成，总行数: {total_rows}")
    print(f"分类数量: {len(global_category_data)}")
    
    # 分离小分类和大分类
    small_categories = {}
    large_categories = {}
    
    for category, rows in global_category_data.items():
        if len(rows) < SMALL_CATEGORY_THRESHOLD:
            small_categories[category] = rows
        else:
            large_categories[category] = rows
    
    print(f"大分类数量: {len(large_categories)}")
    print(f"小分类数量: {len(small_categories)}")
    
    # 处理大分类
    for category, rows in large_categories.items():
        create_category_files(category, global_headers, rows, max_rows_per_file)
    
    # 合并小分类
    if small_categories:
        print(f"\n开始合并小分类...")
        all_small_rows = []
        small_category_names = []
        
        for category, rows in small_categories.items():
            all_small_rows.extend(rows)
            small_category_names.append(category)
            print(f"小分类 '{category}': {len(rows)} 个产品")
        
        print(f"小分类合并完成，总计 {len(all_small_rows)} 个产品")
        print(f"包含的小分类: {', '.join(small_category_names)}")
        
        # 创建合并后的小分类文件
        create_category_files("SmallCategories", global_headers, all_small_rows, max_rows_per_file)

def create_category_files(category, headers, rows, max_rows_per_file):
    """为指定分类创建输出文件"""
    print(f"为分类 '{category}' 创建文件，共 {len(rows)} 行数据")
    
    if len(rows) <= max_rows_per_file:
        # 单个文件即可
        filename = f"{category}_1_{len(rows)}.csv"
        output_path = os.path.join("output", filename)
        write_csv_chunk(output_path, headers, rows)
    else:
        # 需要分割成多个文件
        file_index = 1
        for i in range(0, len(rows), max_rows_per_file):
            chunk = rows[i:i + max_rows_per_file]
            filename = f"{category}_{file_index}_{len(chunk)}.csv"
            output_path = os.path.join("output", filename)
            write_csv_chunk(output_path, headers, chunk)
            file_index += 1

def write_csv_chunk(filename, headers, rows):
    """写入CSV文件块"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            writer.writerows(rows)
        print(f"已创建文件: {os.path.basename(filename)} (包含 {len(rows)} 行数据)")
    except Exception as e:
        print(f"写入文件 {filename} 时出错: {e}")

def main():
    """主函数"""
    print("WooCommerce CSV分割工具 - 优化版本")
    print("支持合并不同文件中的相同分类")
    print("支持小分类合并功能")
    print("输入文件夹: input")
    print("输出文件夹: output")
    print("最大行数限制: 30000")
    print(f"小分类合并阈值: {SMALL_CATEGORY_THRESHOLD}")
    print("=" * 60)
    
    # 确保输出文件夹存在
    if not os.path.exists("output"):
        os.makedirs("output")
        print("创建输出文件夹: output")
    
    # 清空输出文件夹
    for file in os.listdir("output"):
        if file.endswith('.csv'):
            os.remove(os.path.join("output", file))
    print("清空输出文件夹")
    
    # 自动获取input文件夹下的所有CSV文件
    input_files = []
    if os.path.exists("input"):
        for file in os.listdir("input"):
            if file.lower().endswith('.csv'):
                input_files.append(file)
    
    if not input_files:
        print("错误：input文件夹中没有找到CSV文件！")
        return
    
    print(f"找到 {len(input_files)} 个CSV文件:")
    for file in input_files:
        print(f"  - {file}")
    print()
    
    # 处理所有文件并合并相同分类
    process_all_files(input_files)
    
    print("所有文件处理完成！")

if __name__ == "__main__":
    main()