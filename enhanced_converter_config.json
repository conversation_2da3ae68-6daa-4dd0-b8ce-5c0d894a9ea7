{"deduplication": {"enabled": true, "method": "name_image", "similarity_threshold": 0.9, "log_duplicates": true}, "output": {"products_per_file": 50000, "file_prefix": "woocommerce_products", "include_variations": true, "max_variations_per_product": 50}, "product_processing": {"auto_generate_sku": true, "sku_prefix": "WC", "price_adjustment": true, "html_cleanup": true, "image_optimization": true, "max_images_per_product": 5}, "variation_detection": {"enabled": true, "attribute_separators": ["|", ","], "max_attributes": 6, "create_parent_product": true}, "field_mappings": {"post_title": "Name", "regular_price": "Regular price", "sku": "SKU", "stock": "Stock", "tax:product_cat": "Categories", "images": "Images", "attribute:Brand": "Brands", "attribute:MPN": "MPN", "attribute:UPC": "UPC"}, "data_validation": {"required_fields": ["Name"], "price_min": 0, "price_max": 999999, "name_min_length": 3, "name_max_length": 255, "skip_invalid_products": true}, "price_calculation": {"enable_sale_price": true, "discount_tiers": {"0-50": 0.56, "51-100": 0.5, "101-150": 0.45, "151-200": 0.42, "201-250": 0.36, "251-300": 0.33, "301-500": 0.26, "501-1000": 0.22, "1000+": 0.15}}, "logging": {"level": "INFO", "file_rotation": true, "max_file_size_mb": 10, "backup_count": 5, "log_duplicates": true, "log_variations": true}}