#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查HTML标签处理情况
"""

import pandas as pd
from pathlib import Path
import re

def check_html_processing():
    """检查HTML处理情况"""
    csv_file = Path('woocommerce_output_de/bauhaus-at-de-图片前两图_woocommerce.csv')
    
    if not csv_file.exists():
        print("❌ 文件不存在")
        return
    
    df = pd.read_csv(csv_file, encoding='utf-8-sig', nrows=5)
    print('=== HTML标签替换检查 ===')
    print(f'文件: {csv_file.name}')
    print(f'样本数量: {len(df)}')
    
    # 检查Description字段的HTML处理
    for i in range(min(3, len(df))):
        desc = str(df.iloc[i]['Description'])
        print(f'\n第{i+1}个产品描述:')
        print(f'长度: {len(desc)} 字符')
        print(f'内容预览: {desc[:150]}...')
        
        # 检查是否有HTML标签
        html_tags = re.findall(r'<[^>]+>', desc)
        if html_tags:
            print(f'发现HTML标签: {html_tags[:5]}')  # 显示前5个标签
            
            # 检查智能替换的标签
            if '<h3>' in desc:
                print('✅ 发现h3标签（智能替换h1/h2）')
            if '<span>' in desc:
                print('✅ 发现span标签（智能替换font）')
            if 'text-align: center' in desc:
                print('✅ 发现居中样式（智能替换center）')
            
            # 检查危险标签是否被移除
            dangerous_found = False
            for tag in ['script', 'style', 'iframe']:
                if f'<{tag}' in desc.lower():
                    print(f'❌ 发现危险标签: {tag}')
                    dangerous_found = True
            
            if not dangerous_found:
                print('✅ 危险标签已清理')
        else:
            print('⚠️  无HTML标签（可能是纯文本或已完全清理）')
    
    # 检查价格数据
    print(f'\n=== 价格计算检查 ===')
    for i in range(min(3, len(df))):
        regular_price = df.iloc[i]['Regular price']
        sale_price = df.iloc[i]['Sale price']
        
        print(f'第{i+1}个产品:')
        print(f'  常规价: {regular_price}')
        print(f'  促销价: {sale_price}')
        
        if regular_price > 0 and sale_price > 0:
            discount_rate = sale_price / regular_price
            print(f'  折扣率: {discount_rate:.2f} ({discount_rate*100:.1f}%)')
            
            if 0.1 <= discount_rate <= 0.6:
                print('  ✅ 价格计算正确')
            else:
                print('  ❌ 价格计算可能有问题')
        else:
            print('  ⚠️  价格数据异常')

if __name__ == "__main__":
    check_html_processing()
