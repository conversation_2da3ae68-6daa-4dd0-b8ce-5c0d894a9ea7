#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试德国源数据转换脚本
"""

import pandas as pd
from pathlib import Path
from de_source_converter import DESourceConverter
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_file():
    """测试单个文件转换"""
    converter = DESourceConverter()
    
    # 测试第一个文件
    input_file = Path("源数据文件/de/bauhaus-at-de-图片前两图.xlsx")
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    if input_file.exists():
        logger.info(f"测试文件: {input_file.name}")
        success = converter.process_file(input_file, output_dir)
        
        if success:
            # 检查输出文件
            output_file = output_dir / f"{input_file.stem}_woocommerce.csv"
            if output_file.exists():
                df = pd.read_csv(output_file)
                logger.info(f"✅ 转换成功！输出文件: {output_file}")
                logger.info(f"输出行数: {len(df)}")
                logger.info(f"输出列数: {len(df.columns)}")
                
                # 显示前几行
                print("\n前3行数据预览:")
                print(df[['SKU', 'Name', 'Regular price', 'Categories', 'Brands']].head(3).to_string())
                
                return True
            else:
                logger.error("输出文件未生成")
                return False
        else:
            logger.error("转换失败")
            return False
    else:
        logger.error(f"输入文件不存在: {input_file}")
        return False

def test_field_mapping():
    """测试字段映射"""
    converter = DESourceConverter()
    
    # 模拟数据行
    test_row = {
        'ID': 1,
        'title': 'Test Product Name',
        'Brand': 'Test Brand',
        'price/100': 12999,  # 应该转换为 129.99
        'detail': '<p>This is a test description</p>',
        'cate': 'Electronics>Computers>Laptops',
        'image0': 'https://example.com/image1.jpg',
        'mpn': 'TEST-MPN-123'
    }
    
    logger.info("测试字段映射...")
    wc_row = converter.convert_row_to_wc(test_row, 'bauhaus_format')
    
    print("\n字段映射结果:")
    print(f"Name: {wc_row['Name']}")
    print(f"SKU: {wc_row['SKU']}")
    print(f"Regular price: {wc_row['Regular price']}")
    print(f"Sale price: {wc_row['Sale price']}")
    print(f"Categories: {wc_row['Categories']}")
    print(f"Images: {wc_row['Images']}")
    print(f"Brands: {wc_row['Brands']}")
    print(f"Attribute 2 (MPN): {wc_row['Attribute 2 value(s)']}")
    
    # 验证价格转换
    expected_price = 129.99
    if abs(wc_row['Regular price'] - expected_price) < 0.01:
        logger.info("✅ 价格转换正确")
    else:
        logger.error(f"❌ 价格转换错误，期望: {expected_price}, 实际: {wc_row['Regular price']}")
    
    return True

if __name__ == "__main__":
    print("🧪 测试德国源数据转换脚本")
    print("="*60)
    
    # 测试字段映射
    test_field_mapping()
    
    print("\n" + "="*60)
    
    # 测试文件转换
    test_single_file()
    
    print("\n🎉 测试完成！")
