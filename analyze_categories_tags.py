#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析分类和标签优化效果，检查纯数字标签问题
"""

import pandas as pd
import re
from pathlib import Path
from collections import Counter

def analyze_categories_and_tags():
    """分析分类和标签的优化效果"""
    
    print("🔍 分类和标签优化效果分析")
    print("="*60)
    
    output_dir = Path("woocommerce_output_profishop_ultimate")
    
    if not output_dir.exists():
        print("❌ 输出目录不存在!")
        return
    
    csv_files = list(output_dir.glob("*_ultimate.csv"))
    
    if not csv_files:
        print("❌ 没有找到转换结果文件!")
        return
    
    print(f"📊 找到 {len(csv_files)} 个转换结果文件")
    
    all_categories = []
    all_tags = []
    numeric_tags = []
    problematic_tags = []
    
    for i, csv_file in enumerate(csv_files[:2], 1):  # 分析前2个文件
        print(f"\n[{i}/2] 分析文件: {csv_file.name}")
        print("-" * 50)
        
        try:
            # 读取转换结果 (前1000行样本)
            df = pd.read_csv(csv_file, encoding='utf-8-sig', nrows=1000)
            
            print(f"📊 基本信息:")
            print(f"  样本数量: {len(df)}")
            
            # 分析分类
            if 'Categories' in df.columns:
                categories = df['Categories'].fillna('').tolist()
                non_empty_cats = [cat for cat in categories if cat.strip()]
                
                print(f"\n📂 分类分析:")
                print(f"  有分类: {len(non_empty_cats)}/{len(df)} ({len(non_empty_cats)/len(df)*100:.1f}%)")
                
                # 分类样本
                unique_cats = list(set(non_empty_cats))[:10]
                print(f"  分类样本:")
                for cat in unique_cats:
                    print(f"    • {cat}")
                
                # 检查多层分类
                multi_level = [cat for cat in non_empty_cats if '>' in cat]
                print(f"  多层分类: {len(multi_level)}/{len(non_empty_cats)} ({len(multi_level)/len(non_empty_cats)*100:.1f}%)")
                
                all_categories.extend(non_empty_cats)
            
            # 分析标签
            if 'Tags' in df.columns:
                tags_data = df['Tags'].fillna('').tolist()
                non_empty_tags = [tag for tag in tags_data if tag.strip()]
                
                print(f"\n🏷️ 标签分析:")
                print(f"  有标签: {len(non_empty_tags)}/{len(df)} ({len(non_empty_tags)/len(df)*100:.1f}%)")
                
                # 解析所有标签
                individual_tags = []
                for tag_str in non_empty_tags:
                    if ',' in tag_str:
                        tags = [t.strip() for t in tag_str.split(',') if t.strip()]
                    else:
                        tags = [tag_str.strip()]
                    individual_tags.extend(tags)
                
                print(f"  总标签数: {len(individual_tags)}")
                print(f"  唯一标签: {len(set(individual_tags))}")
                
                # 检查纯数字标签
                numeric_only = [tag for tag in individual_tags if tag.isdigit()]
                numeric_tags.extend(numeric_only)
                
                # 检查问题标签
                problematic = []
                for tag in individual_tags:
                    if tag.isdigit():
                        problematic.append(f"纯数字: {tag}")
                    elif len(tag) <= 2:
                        problematic.append(f"太短: {tag}")
                    elif not re.match(r'^[a-zA-Z0-9\s\-_]+$', tag):
                        problematic.append(f"特殊字符: {tag}")
                
                problematic_tags.extend(problematic)
                
                print(f"  纯数字标签: {len(numeric_only)}")
                print(f"  问题标签: {len(problematic)}")
                
                # 标签样本
                tag_counter = Counter(individual_tags)
                print(f"  前10标签:")
                for tag, count in tag_counter.most_common(10):
                    print(f"    • {tag}: {count} 次")
                
                all_tags.extend(individual_tags)
        
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    # 生成总结报告
    print(f"\n" + "="*60)
    print(f"📈 分类和标签优化总结报告")
    print("="*60)
    
    if all_categories:
        print(f"\n📂 分类总结:")
        print(f"  总分类数: {len(all_categories)}")
        print(f"  唯一分类: {len(set(all_categories))}")
        
        # 分类分布
        cat_counter = Counter(all_categories)
        print(f"  前10分类:")
        for cat, count in cat_counter.most_common(10):
            print(f"    • {cat}: {count} 次")
        
        # 多层分类统计
        multi_level_all = [cat for cat in all_categories if '>' in cat]
        print(f"  多层分类率: {len(multi_level_all)}/{len(all_categories)} ({len(multi_level_all)/len(all_categories)*100:.1f}%)")
    
    if all_tags:
        print(f"\n🏷️ 标签总结:")
        print(f"  总标签数: {len(all_tags)}")
        print(f"  唯一标签: {len(set(all_tags))}")
        
        # 标签质量分析
        print(f"\n🔍 标签质量分析:")
        print(f"  纯数字标签: {len(numeric_tags)} 个")
        print(f"  问题标签: {len(problematic_tags)} 个")
        
        if numeric_tags:
            print(f"  纯数字标签样本:")
            unique_numeric = list(set(numeric_tags))[:10]
            for num_tag in unique_numeric:
                print(f"    • {num_tag}")
        
        # 标签长度分析
        tag_lengths = [len(tag) for tag in all_tags]
        avg_length = sum(tag_lengths) / len(tag_lengths)
        print(f"  平均标签长度: {avg_length:.1f} 字符")
        
        short_tags = [tag for tag in all_tags if len(tag) <= 2]
        print(f"  过短标签(≤2字符): {len(short_tags)} 个")
        
        # 标签分布
        tag_counter = Counter(all_tags)
        print(f"  前15标签:")
        for tag, count in tag_counter.most_common(15):
            print(f"    • {tag}: {count} 次")

def analyze_original_vs_optimized():
    """对比原始数据和优化后的标签"""
    
    print(f"\n🔄 原始数据 vs 优化后对比分析")
    print("="*60)
    
    # 读取原始数据样本
    source_dir = Path("源数据文件/de")
    profishop_files = list(source_dir.glob("profishop-de*.xlsx"))
    
    if profishop_files:
        original_file = profishop_files[0]
        print(f"📊 分析原始文件: {original_file.name}")
        
        try:
            original_df = pd.read_excel(original_file, nrows=100)
            
            if 'tags' in original_df.columns:
                original_tags = original_df['tags'].fillna('').tolist()
                
                print(f"\n🏷️ 原始标签分析:")
                print(f"  样本数: {len(original_tags)}")
                
                # 解析原始标签
                original_individual = []
                for tag_str in original_tags:
                    if not tag_str or str(tag_str).strip() == 'nan':
                        continue
                    
                    tag_str = str(tag_str)
                    
                    # 检测分隔符
                    if ',' in tag_str:
                        tags = [t.strip() for t in tag_str.split(',')]
                    elif ';' in tag_str:
                        tags = [t.strip() for t in tag_str.split(';')]
                    else:
                        tags = [tag_str.strip()]
                    
                    original_individual.extend([t for t in tags if t])
                
                print(f"  原始标签总数: {len(original_individual)}")
                print(f"  原始唯一标签: {len(set(original_individual))}")
                
                # 原始标签样本
                print(f"  原始标签样本:")
                for tag in original_individual[:10]:
                    print(f"    • {tag}")
                
                # 检查原始数据中的纯数字标签
                original_numeric = [tag for tag in original_individual if tag.isdigit()]
                print(f"  原始纯数字标签: {len(original_numeric)} 个")
                
                if original_numeric:
                    print(f"  原始纯数字样本:")
                    for num_tag in original_numeric[:5]:
                        print(f"    • {num_tag}")
        
        except Exception as e:
            print(f"❌ 原始数据分析失败: {e}")

def suggest_tag_improvements():
    """建议标签改进方案"""
    
    print(f"\n💡 标签优化改进建议")
    print("="*60)
    
    print(f"🔍 发现的问题:")
    print(f"  1. 纯数字标签存在 (如: 123, 456)")
    print(f"  2. 过短标签存在 (如: a, b)")
    print(f"  3. 可能包含特殊字符")
    print(f"  4. 德语停用词可能未完全过滤")
    
    print(f"\n✅ 改进方案:")
    print(f"  1. 增强数字标签过滤:")
    print(f"     • 完全移除纯数字标签")
    print(f"     • 保留有意义的数字组合 (如: 24V, 220V)")
    
    print(f"  2. 长度过滤优化:")
    print(f"     • 最小长度提升到3字符")
    print(f"     • 最大长度限制到20字符")
    
    print(f"  3. 德语停用词扩展:")
    print(f"     • 添加更多德语停用词")
    print(f"     • 添加英语停用词")
    
    print(f"  4. 标签语义化:")
    print(f"     • 基于产品类别生成相关标签")
    print(f"     • 品牌名称作为主要标签")
    
    print(f"  5. 标签数量优化:")
    print(f"     • 限制每个产品最多5-8个标签")
    print(f"     • 优先保留高价值标签")

def main():
    """主函数"""
    analyze_categories_and_tags()
    analyze_original_vs_optimized()
    suggest_tag_improvements()

if __name__ == "__main__":
    main()
