#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面分析所有德国源数据文件
"""

import pandas as pd
from pathlib import Path
import logging
from collections import defaultdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_single_file(file_path):
    """分析单个文件的详细结构"""
    try:
        # 读取前几行数据
        df = pd.read_excel(file_path, nrows=3)
        
        result = {
            'file_name': file_path.name,
            'file_size_mb': round(file_path.stat().st_size / 1024 / 1024, 2),
            'total_columns': len(df.columns),
            'sample_rows': len(df),
            'columns': list(df.columns),
            'sample_data': {},
            'field_analysis': {},
            'potential_issues': []
        }
        
        # 分析每一列的数据
        for col in df.columns:
            sample_values = []
            for i in range(min(3, len(df))):
                value = df.iloc[i][col]
                if pd.notna(value):
                    sample_values.append(str(value)[:100])  # 限制长度
                else:
                    sample_values.append('NaN')
            
            result['sample_data'][col] = sample_values
            
            # 字段类型分析
            result['field_analysis'][col] = analyze_field_type(col, sample_values)
        
        # 检查潜在问题
        result['potential_issues'] = check_potential_issues(result)
        
        return result
        
    except Exception as e:
        logger.error(f"分析文件 {file_path.name} 失败: {e}")
        return {
            'file_name': file_path.name,
            'error': str(e),
            'potential_issues': ['文件读取失败']
        }

def analyze_field_type(column_name, sample_values):
    """分析字段类型"""
    col_lower = column_name.lower()
    
    # 产品名称字段
    if any(keyword in col_lower for keyword in ['title', 'name', 'product']):
        return 'product_name'
    
    # 价格字段
    elif any(keyword in col_lower for keyword in ['price', 'cost', 'amount']):
        return 'price'
    
    # 描述字段
    elif any(keyword in col_lower for keyword in ['description', 'detail', 'content', 'desc']):
        return 'description'
    
    # 分类字段
    elif any(keyword in col_lower for keyword in ['category', 'cate', 'cat']):
        return 'category'
    
    # 图片字段
    elif any(keyword in col_lower for keyword in ['image', 'img', 'photo', 'pic']):
        return 'image'
    
    # SKU字段
    elif any(keyword in col_lower for keyword in ['sku', 'id']):
        return 'sku'
    
    # 品牌字段
    elif any(keyword in col_lower for keyword in ['brand', 'manufacturer']):
        return 'brand'
    
    # 属性字段
    elif any(keyword in col_lower for keyword in ['mpn', 'upc', 'ean', 'model']):
        return 'attribute'
    
    else:
        return 'unknown'

def check_potential_issues(file_result):
    """检查潜在问题"""
    issues = []
    
    # 检查是否有产品名称字段
    name_fields = [col for col, type_info in file_result['field_analysis'].items() 
                   if type_info == 'product_name']
    if not name_fields:
        issues.append("缺少产品名称字段")
    
    # 检查是否有价格字段
    price_fields = [col for col, type_info in file_result['field_analysis'].items() 
                    if type_info == 'price']
    if not price_fields:
        issues.append("缺少价格字段")
    
    # 检查特殊价格格式
    for col in file_result['columns']:
        if 'price/100' in col.lower():
            issues.append(f"特殊价格格式: {col} (需要除以100)")
    
    # 检查图片字段格式
    image_fields = [col for col, type_info in file_result['field_analysis'].items() 
                    if type_info == 'image']
    for col in image_fields:
        sample_data = file_result['sample_data'].get(col, [])
        for sample in sample_data:
            if '|' in str(sample):
                issues.append(f"多图片格式: {col} (使用|分隔)")
                break
    
    # 检查分类字段格式
    category_fields = [col for col, type_info in file_result['field_analysis'].items() 
                       if type_info == 'category']
    for col in category_fields:
        sample_data = file_result['sample_data'].get(col, [])
        for sample in sample_data:
            if '>' in str(sample):
                issues.append(f"分类格式: {col} (使用>分隔)")
                break
    
    return issues

def main():
    """主分析函数"""
    source_dir = Path("源数据文件/de")
    
    if not source_dir.exists():
        print("❌ 源数据目录不存在!")
        return
    
    # 获取所有Excel文件
    excel_files = [f for f in source_dir.glob("*.xlsx") if not f.name.startswith('~')]
    
    print(f"📁 找到 {len(excel_files)} 个Excel文件")
    print("="*80)
    
    all_results = []
    field_statistics = defaultdict(int)
    issue_statistics = defaultdict(int)
    
    # 分析每个文件
    for i, file_path in enumerate(excel_files, 1):
        print(f"\n{i:2d}. 分析文件: {file_path.name}")
        print("-" * 60)
        
        result = analyze_single_file(file_path)
        all_results.append(result)
        
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            continue
        
        print(f"文件大小: {result['file_size_mb']} MB")
        print(f"列数: {result['total_columns']}")
        print(f"样本行数: {result['sample_rows']}")
        
        # 显示字段类型分析
        print("字段类型分析:")
        for col, field_type in result['field_analysis'].items():
            print(f"  {field_type:12s}: {col}")
            field_statistics[field_type] += 1
        
        # 显示潜在问题
        if result['potential_issues']:
            print("⚠️  潜在问题:")
            for issue in result['potential_issues']:
                print(f"  - {issue}")
                issue_statistics[issue] += 1
        else:
            print("✅ 无明显问题")
    
    # 生成总结报告
    print("\n" + "="*80)
    print("📊 总结报告")
    print("="*80)
    
    print(f"总文件数: {len(excel_files)}")
    print(f"成功分析: {len([r for r in all_results if 'error' not in r])}")
    print(f"分析失败: {len([r for r in all_results if 'error' in r])}")
    
    print("\n字段类型统计:")
    for field_type, count in sorted(field_statistics.items()):
        print(f"  {field_type:15s}: {count} 个字段")
    
    print("\n问题统计:")
    if issue_statistics:
        for issue, count in sorted(issue_statistics.items()):
            print(f"  {issue}: {count} 个文件")
    else:
        print("  无问题发现")
    
    # 兼容性评估
    print("\n🔧 脚本兼容性评估:")
    
    # 检查必需字段覆盖率
    files_with_names = len([r for r in all_results if 'error' not in r and 
                           any(t == 'product_name' for t in r['field_analysis'].values())])
    files_with_prices = len([r for r in all_results if 'error' not in r and 
                            any(t == 'price' for t in r['field_analysis'].values())])
    
    print(f"有产品名称字段的文件: {files_with_names}/{len(excel_files)} ({files_with_names/len(excel_files)*100:.1f}%)")
    print(f"有价格字段的文件: {files_with_prices}/{len(excel_files)} ({files_with_prices/len(excel_files)*100:.1f}%)")
    
    # 特殊格式统计
    special_formats = len([r for r in all_results if 'error' not in r and 
                          any('特殊价格格式' in issue for issue in r['potential_issues'])])
    multi_image_formats = len([r for r in all_results if 'error' not in r and 
                              any('多图片格式' in issue for issue in r['potential_issues'])])
    
    print(f"需要特殊价格处理的文件: {special_formats}")
    print(f"需要多图片处理的文件: {multi_image_formats}")
    
    # 给出建议
    print("\n💡 建议:")
    if files_with_names == len(excel_files) and files_with_prices == len(excel_files):
        print("✅ 所有文件都有必需字段，脚本应该可以处理")
    else:
        print("⚠️  部分文件缺少必需字段，可能需要手动检查")
    
    if special_formats > 0:
        print("⚠️  部分文件有特殊价格格式，确保脚本支持price/100等格式")
    
    if multi_image_formats > 0:
        print("⚠️  部分文件有多图片格式，确保脚本支持|分隔符")
    
    return all_results

if __name__ == "__main__":
    results = main()
