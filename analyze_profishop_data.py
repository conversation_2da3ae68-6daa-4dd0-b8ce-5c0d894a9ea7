#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析profishop-de数据结构
"""

import pandas as pd
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_profishop_file(file_path):
    """分析单个profishop文件"""
    try:
        print(f"\n{'='*80}")
        print(f"📁 分析文件: {file_path.name}")
        print('='*80)
        
        # 读取前10行数据
        df = pd.read_excel(file_path, nrows=10)
        
        print(f"📊 基本信息:")
        print(f"  总列数: {len(df.columns)}")
        print(f"  数据行数: {len(df)}")
        
        # 显示所有列名
        print(f"\n📋 所有列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 重点分析关键列
        key_columns = {
            'all-description l': 'all-description l',
            'price': None,
            'image l': 'image l', 
            's-image l': 's-image l'
        }
        
        # 查找价格相关列
        price_cols = [col for col in df.columns if 'price' in str(col).lower()]
        if price_cols:
            key_columns['price'] = price_cols[0]
        
        print(f"\n🔍 关键列分析:")
        for key, actual_col in key_columns.items():
            if actual_col and actual_col in df.columns:
                print(f"  ✅ {key}: 找到列 '{actual_col}'")
                
                # 显示样本数据
                sample_data = df[actual_col].head(3)
                print(f"     样本数据:")
                for i, val in enumerate(sample_data, 1):
                    val_str = str(val)[:100] + "..." if len(str(val)) > 100 else str(val)
                    print(f"       {i}: {val_str}")
                
                # 统计信息
                null_count = df[actual_col].isnull().sum()
                unique_count = df[actual_col].nunique()
                print(f"     空值数量: {null_count}/{len(df)}")
                print(f"     唯一值数量: {unique_count}")
                
            else:
                print(f"  ❌ {key}: 未找到对应列")
                # 尝试模糊匹配
                similar_cols = [col for col in df.columns if key.replace(' ', '').lower() in str(col).replace(' ', '').lower()]
                if similar_cols:
                    print(f"     可能的相似列: {similar_cols}")
        
        # 价格分析
        if key_columns['price']:
            price_col = key_columns['price']
            prices = pd.to_numeric(df[price_col], errors='coerce')
            valid_prices = prices.dropna()
            
            if len(valid_prices) > 0:
                print(f"\n💰 价格分析:")
                print(f"  价格列: '{price_col}'")
                print(f"  有效价格数量: {len(valid_prices)}/{len(df)}")
                print(f"  价格范围: {valid_prices.min():.2f} - {valid_prices.max():.2f}")
                print(f"  平均价格: {valid_prices.mean():.2f}")
                
                # 小于10的价格统计
                low_prices = valid_prices[valid_prices < 10]
                print(f"  小于10的价格: {len(low_prices)} 个 ({len(low_prices)/len(valid_prices)*100:.1f}%)")
                if len(low_prices) > 0:
                    print(f"    示例: {low_prices.head().tolist()}")
        
        # 图片字段分析
        image_analysis = {}
        for img_key in ['image l', 's-image l']:
            if key_columns.get(img_key.replace(' ', '_'), img_key) in df.columns:
                col_name = key_columns.get(img_key.replace(' ', '_'), img_key)
                img_data = df[col_name]
                
                non_null = img_data.notna().sum()
                has_content = (img_data.notna() & (img_data != '')).sum()
                
                image_analysis[img_key] = {
                    'column': col_name,
                    'non_null': non_null,
                    'has_content': has_content,
                    'coverage': has_content / len(df) * 100
                }
        
        if image_analysis:
            print(f"\n🖼️  图片字段分析:")
            for img_key, analysis in image_analysis.items():
                print(f"  {img_key} ('{analysis['column']}'):")
                print(f"    有内容: {analysis['has_content']}/{len(df)} ({analysis['coverage']:.1f}%)")
        
        # SKU分析 - 分析第一列
        first_col = df.iloc[:, 0]
        first_col_name = df.columns[0]
        
        print(f"\n🏷️  SKU分析 (第一列):")
        print(f"  列名: '{first_col_name}'")
        print(f"  数据类型: {first_col.dtype}")
        print(f"  唯一值: {first_col.nunique()}/{len(df)}")
        print(f"  空值: {first_col.isnull().sum()}")
        print(f"  样本值:")
        for i, val in enumerate(first_col.head(3), 1):
            print(f"    {i}: {val}")
        
        return {
            'file_name': file_path.name,
            'columns': list(df.columns),
            'key_columns': key_columns,
            'price_analysis': {
                'column': key_columns.get('price'),
                'low_price_count': len(low_prices) if 'low_prices' in locals() else 0,
                'total_valid': len(valid_prices) if 'valid_prices' in locals() else 0
            },
            'image_analysis': image_analysis,
            'first_column': {
                'name': first_col_name,
                'unique_ratio': first_col.nunique() / len(df),
                'null_count': first_col.isnull().sum()
            }
        }
        
    except Exception as e:
        logger.error(f"分析文件失败: {e}")
        return None

def main():
    """主函数"""
    # profishop-de数据文件路径
    source_dir = Path("源数据文件/de")
    
    if not source_dir.exists():
        print("❌ 德国数据目录不存在!")
        return
    
    # 获取profishop-de相关文件
    profishop_files = [f for f in source_dir.glob("profishop-de*.xlsx") if not f.name.startswith('~')]
    
    if not profishop_files:
        print("❌ 没有找到profishop-de文件!")
        return
    
    print("🚀 Profishop-DE 数据结构专项分析")
    print(f"📂 数据目录: {source_dir}")
    print(f"📊 找到 {len(profishop_files)} 个profishop-de文件")
    
    # 分析所有profishop文件
    analysis_results = []
    
    for i, file_path in enumerate(profishop_files, 1):
        print(f"\n[{i}/{len(profishop_files)}] 正在分析...")
        result = analyze_profishop_file(file_path)
        if result:
            analysis_results.append(result)
    
    # 生成汇总报告
    if analysis_results:
        print("\n" + "="*80)
        print("📈 Profishop-DE 汇总分析报告")
        print("="*80)
        
        print(f"\n📊 文件列表:")
        for result in analysis_results:
            print(f"  • {result['file_name']}")
        
        # 列名一致性分析
        all_columns = set()
        for result in analysis_results:
            all_columns.update(result['columns'])
        
        print(f"\n📋 列名统计:")
        print(f"  总共发现 {len(all_columns)} 个不同的列名")
        
        # 关键列覆盖率
        key_column_coverage = {
            'all-description l': 0,
            'price': 0,
            'image l': 0,
            's-image l': 0
        }
        
        for result in analysis_results:
            for key in key_column_coverage:
                if result['key_columns'].get(key):
                    key_column_coverage[key] += 1
        
        print(f"\n🔍 关键列覆盖率:")
        for key, count in key_column_coverage.items():
            coverage = count / len(analysis_results) * 100
            print(f"  {key}: {count}/{len(analysis_results)} ({coverage:.1f}%)")
        
        # 价格分析汇总
        total_low_prices = sum(r['price_analysis']['low_price_count'] for r in analysis_results)
        total_valid_prices = sum(r['price_analysis']['total_valid'] for r in analysis_results)
        
        if total_valid_prices > 0:
            print(f"\n💰 价格分析汇总:")
            print(f"  总有效价格: {total_valid_prices}")
            print(f"  小于10的价格: {total_low_prices} ({total_low_prices/total_valid_prices*100:.1f}%)")
        
        # 优化建议
        print(f"\n💡 数据优化建议:")
        print("  1. ✅ 使用 'all-description l' 作为主要描述字段")
        print("  2. ✅ 价格小于10的商品不应用折扣规则")
        print("  3. ✅ 'image l' 缺失时使用 's-image l' 替补")
        print("  4. 🔍 第一列适合作为SKU基础的文件数量需要进一步分析")
        print("  5. 📊 建议按文件分别转换，保持数据源的一致性")
    
    else:
        print("❌ 没有成功分析任何文件")

if __name__ == "__main__":
    main()
