# 🔧 WooCommerce转换脚本通用功能规则

## 📋 **核心功能清单**
*每次修改脚本时必须检查的功能*

### 1. 🏷️ **字段映射规则**

#### **必须支持的字段映射**
```python
STANDARD_FIELD_MAPPINGS = {
    # 产品名称字段 - 优先级从高到低
    'name_fields': ['title', 'post_title', 'Name', 'product_name'],
    
    # 价格字段 - 注意特殊格式
    'price_fields': ['price', 'regular_price', 'price/100', 'cost'],
    
    # 描述字段
    'description_fields': ['description', 'detail', 'post_content', 'desc'],
    'short_desc_fields': ['description s', 'post_excerpt', 'short_description'],
    
    # 分类字段
    'category_fields': ['category', 'cate', 'tax:product_cat', 'Categories'],
    
    # 图片字段
    'image_fields': ['image', 'images', 'image0', 'featured', 'photo'],
    
    # SKU字段
    'sku_fields': ['sku', 'SKU', 'ID', 'product_id'],
    
    # 品牌字段
    'brand_fields': ['Brand', 'attribute:Brand', 'brand', 'manufacturer'],
    
    # 属性字段
    'mpn_fields': ['mpn', 'MPN', 'MFG', 'model'],
    'upc_fields': ['UPC', 'upc', 'barcode', 'ean'],
}
```

### 2. 💰 **价格计算规则**

#### **常规价格调整**
```python
def calculate_regular_price(original_price: float) -> float:
    """标准价格调整逻辑 - 不可修改"""
    if original_price < 3:
        discount = 0
    elif original_price <= 10:
        discount = 1
    elif original_price <= 50:
        discount = 2
    else:
        discount = 3
    
    return round(max(original_price - discount, 0.01), 2)
```

#### **促销价格计算**
```python
def calculate_sale_price(regular_price: float) -> float:
    """标准促销价格逻辑 - 不可修改"""
    price_tiers = [
        (50, 0.56), (100, 0.5), (150, 0.45), (200, 0.42),
        (250, 0.36), (300, 0.33), (350, 0.31), (500, 0.26),
        (750, 0.24), (1000, 0.22), (2000, 0.2), (float('inf'), 0.15)
    ]
    
    for threshold, multiplier in price_tiers:
        if regular_price <= threshold:
            return round(regular_price * multiplier, 2)
```

#### **特殊价格格式处理**
```python
PRICE_FORMAT_HANDLERS = {
    'price/100': lambda x: float(x) / 100,  # 需要除以100
    'price_cents': lambda x: float(x) / 100,  # 分为单位
    'price_with_currency': lambda x: float(re.sub(r'[^\d.]', '', str(x))),  # 移除货币符号
}
```

### 3. 🏷️ **HTML清理规则**

#### **智能标签替换**
```python
HTML_TAG_REPLACEMENTS = {
    r'<font[^>]*>': '<span>',
    r'</font>': '</span>',
    r'<center[^>]*>': '<div style="text-align: center;">',
    r'</center>': '</div>',
    r'<h1[^>]*>': '<h3>',  # SEO优化
    r'</h1>': '</h3>',
    r'<h2[^>]*>': '<h3>',  # SEO优化
    r'</h2>': '</h3>',
}

DANGEROUS_TAGS = ['script', 'style', 'iframe', 'noscript', 'object', 'embed']
```

### 4. 📂 **分类处理规则**

#### **分隔符标准化**
```python
CATEGORY_SEPARATORS = [
    ('|||', ' > '),  # 三竖线转换为标准分隔符
    ('>', ' > '),    # 单个>转换为带空格的>
    (',', ' > '),    # 逗号转换
    (';', ' > '),    # 分号转换
]

def process_categories(category_str: str) -> str:
    """标准分类处理"""
    # 1. 应用分隔符转换
    # 2. 去重和清理
    # 3. 限制层级数量（最多4级）
    # 4. 返回标准格式
```

### 5. 🖼️ **图片处理规则**

#### **图片分隔符处理**
```python
IMAGE_SEPARATORS = ['|||', '|', ',', ';', '\n']

def process_images(image_str: str) -> str:
    """标准图片处理"""
    # 1. 提取所有URL
    # 2. 验证URL有效性
    # 3. 去重处理
    # 4. 限制数量（最多5张）
    # 5. 返回逗号分隔的字符串
```

#### **复杂图片格式处理**
```python
def extract_complex_images(html_image_str: str) -> List[str]:
    """处理HTML格式的图片字符串"""
    # 1. 提取srcset属性
    # 2. 清理HTML标签
    # 3. 处理多种分隔符
    # 4. 验证和去重
```

### 6. 🏷️ **SKU生成规则**

#### **SKU生成策略**
```python
def generate_sku(name: str, brand: str = "", original_id: str = "", prefix: str = "WC") -> str:
    """标准SKU生成逻辑"""
    # 优先级1: 使用原始ID
    if original_id:
        return f"{prefix}-{original_id}"
    
    # 优先级2: 基于品牌+产品名
    # 优先级3: 基于产品名
    # 限制长度: 最多20个字符
    # 确保唯一性: 添加计数器
```

### 7. 📝 **描述生成规则**

#### **描述优先级**
```python
DESCRIPTION_PRIORITY = [
    'post_content',      # 完整描述
    'description',       # 标准描述
    'detail',           # 详情
    'post_excerpt',     # 摘要
    'short_description', # 短描述
]

def generate_description(row: dict) -> str:
    """标准描述生成逻辑"""
    # 1. 按优先级查找现有描述
    # 2. 如果没有，基于产品名生成
    # 3. 应用HTML智能清理
    # 4. 确保最小长度
```

### 8. 🔧 **WooCommerce字段默认值**

#### **必须设置的默认值**
```python
WOOCOMMERCE_DEFAULTS = {
    'Type': 'simple',
    'Published': 1,
    'Is featured?': 0,
    'Visibility in catalog': 'visible',
    'In stock?': 1,
    'Backorders allowed?': 0,  # 重要：必须为0
    'Sold individually?': 0,
    'Allow customer reviews?': 1,
    'Tax status': 'taxable',
    'Tax class': '',
}
```

### 9. 📊 **文件处理规则**

#### **支持的文件格式**
```python
SUPPORTED_FORMATS = {
    '.xlsx': 'pd.read_excel',
    '.xls': 'pd.read_excel', 
    '.csv': 'pd.read_csv',
}

# 输出设置
OUTPUT_SETTINGS = {
    'encoding': 'utf-8-sig',  # 支持中文
    'index': False,           # 不输出行号
    'products_per_file': 50000,  # 每文件产品数
}
```

### 10. 🔍 **数据验证规则**

#### **必须验证的字段**
```python
VALIDATION_RULES = {
    'name': {'required': True, 'min_length': 3, 'max_length': 255},
    'price': {'required': False, 'min_value': 0, 'max_value': 999999},
    'sku': {'required': True, 'unique': True, 'max_length': 20},
    'images': {'required': False, 'url_format': True, 'max_count': 5},
}
```

## 🚨 **关键注意事项**

### **绝对不能修改的规则**
1. **价格计算逻辑** - 分层折扣必须保持一致
2. **Backorders allowed默认值** - 必须为0
3. **HTML标签替换规则** - 保持SEO优化
4. **SKU唯一性** - 必须确保不重复

### **每次修改脚本必须检查**
1. ✅ 字段映射是否完整
2. ✅ 价格计算是否正确
3. ✅ HTML清理是否智能
4. ✅ 图片处理是否支持复杂格式
5. ✅ SKU生成是否唯一
6. ✅ 默认值是否正确
7. ✅ 文件编码是否支持中文
8. ✅ 错误处理是否完善

### **新数据源适配流程**
1. **分析数据结构** - 运行字段分析脚本
2. **更新字段映射** - 添加新的字段名到映射表
3. **检查特殊格式** - 价格、图片、分类的特殊处理
4. **测试转换** - 小批量测试验证
5. **验证输出** - 检查WooCommerce兼容性

## 📝 **使用此规则文档**

每次创建新的转换脚本时：
1. 复制这些核心函数
2. 根据数据源调整字段映射
3. 保持价格和HTML处理逻辑不变
4. 测试所有核心功能
5. 更新此文档（如有新发现）
