# 德国电商数据转换系统 - 技术架构图

## 🏗️ 系统整体架构

```mermaid
graph TB
    subgraph "输入层 Input Layer"
        A1[bauhaus-at-de.xlsx]
        A2[beliani-de.xlsx] 
        A3[bloomled-de.xlsx]
        A4[klickparts-de.xlsx]
        A5[lampenwelt-de.xlsx]
        A6[neyer.de.xlsx]
        A7[obelink-de.xlsx]
        A8[profishop-de.xlsx]
        A9[segmueller-de.xlsx]
        A10[... 其他文件]
    end

    subgraph "处理引擎 Processing Engine"
        B1[文件扫描器 File Scanner]
        B2[格式检测器 Format Detector]
        B3[数据读取器 Data Reader]
        B4[字段映射器 Field Mapper]
        B5[数据清理器 Data Cleaner]
        B6[HTML处理器 HTML Processor]
        B7[价格计算器 Price Calculator]
        B8[去重合并器 Deduplicator]
    end

    subgraph "输出层 Output Layer"
        C1[bauhaus-at-de_woocommerce.csv]
        C2[beliani-de_woocommerce.csv]
        C3[bloomled-de_woocommerce.csv]
        C4[klickparts-de_woocommerce.csv]
        C5[lampenwelt-de_woocommerce.csv]
        C6[neyer.de_woocommerce.csv]
        C7[obelink-de_woocommerce.csv]
        C8[profishop-de_woocommerce.csv]
        C9[segmueller-de_woocommerce.csv]
        C10[... 其他输出文件]
    end

    subgraph "监控层 Monitoring Layer"
        D1[进度监控 Progress Monitor]
        D2[日志记录 Logger]
        D3[错误处理 Error Handler]
        D4[质量检查 Quality Checker]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1
    A6 --> B1
    A7 --> B1
    A8 --> B1
    A9 --> B1
    A10 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5
    B5 --> B6
    B6 --> B7
    B7 --> B8

    B8 --> C1
    B8 --> C2
    B8 --> C3
    B8 --> C4
    B8 --> C5
    B8 --> C6
    B8 --> C7
    B8 --> C8
    B8 --> C9
    B8 --> C10

    B1 --> D1
    B2 --> D2
    B3 --> D3
    B4 --> D4
```

## 🔄 数据处理流程图

```mermaid
flowchart TD
    Start([开始处理]) --> ScanFiles[扫描Excel文件]
    ScanFiles --> ForEach{遍历每个文件}
    
    ForEach -->|有文件| ReadFile[读取Excel文件]
    ForEach -->|无文件| GenerateReport[生成处理报告]
    
    ReadFile --> DetectFormat[检测数据格式]
    DetectFormat --> WordPress{WordPress格式?}
    DetectFormat --> Bauhaus{Bauhaus格式?}
    DetectFormat --> Bloomled{Bloomled格式?}
    
    WordPress -->|是| MapWordPress[应用WordPress映射]
    Bauhaus -->|是| MapBauhaus[应用Bauhaus映射]
    Bloomled -->|是| MapBloomled[应用Bloomled映射]
    
    MapWordPress --> CleanData[数据清理]
    MapBauhaus --> CleanData
    MapBloomled --> CleanData
    
    CleanData --> ProcessHTML[HTML内容处理]
    ProcessHTML --> CalculatePrice[价格计算]
    CalculatePrice --> RemoveDuplicates[去重处理]
    RemoveDuplicates --> ValidateData[数据验证]
    
    ValidateData --> Valid{数据有效?}
    Valid -->|是| SaveCSV[保存CSV文件]
    Valid -->|否| LogError[记录错误]
    
    SaveCSV --> UpdateProgress[更新进度]
    LogError --> UpdateProgress
    UpdateProgress --> ForEach
    
    GenerateReport --> End([处理完成])
```

## 🧩 核心模块架构

```mermaid
classDiagram
    class DataConverter {
        +process_all_files()
        +process_single_file()
        +detect_format()
        +apply_mapping()
        +clean_data()
        +save_output()
    }
    
    class FormatDetector {
        +detect_wordpress_format()
        +detect_bauhaus_format()
        +detect_bloomled_format()
        +get_field_mapping()
    }
    
    class DataCleaner {
        +clean_html_content()
        +calculate_sale_price()
        +process_images()
        +validate_fields()
        +remove_duplicates()
    }
    
    class HTMLProcessor {
        +replace_tags()
        +remove_dangerous_tags()
        +clean_attributes()
        +sanitize_content()
    }
    
    class PriceCalculator {
        +calculate_layered_pricing()
        +apply_category_discount()
        +validate_price_range()
        +format_currency()
    }
    
    class QualityChecker {
        +validate_required_fields()
        +check_price_consistency()
        +verify_image_urls()
        +generate_quality_report()
    }
    
    DataConverter --> FormatDetector
    DataConverter --> DataCleaner
    DataCleaner --> HTMLProcessor
    DataCleaner --> PriceCalculator
    DataConverter --> QualityChecker
```

## 📊 数据流转图

```mermaid
graph LR
    subgraph "原始数据 Raw Data"
        R1[Excel文件]
        R2[多种格式]
        R3[不同字段名]
        R4[HTML内容]
        R5[价格数据]
    end
    
    subgraph "处理阶段 Processing"
        P1[格式统一]
        P2[字段映射]
        P3[内容清理]
        P4[价格计算]
        P5[质量验证]
    end
    
    subgraph "标准输出 Standard Output"
        O1[WooCommerce CSV]
        O2[统一字段]
        O3[安全HTML]
        O4[合理价格]
        O5[完整数据]
    end
    
    R1 --> P1
    R2 --> P1
    R3 --> P2
    R4 --> P3
    R5 --> P4
    
    P1 --> P2
    P2 --> P3
    P3 --> P4
    P4 --> P5
    
    P5 --> O1
    P5 --> O2
    P5 --> O3
    P5 --> O4
    P5 --> O5
```

## 🔧 技术栈架构

```mermaid
graph TB
    subgraph "应用层 Application Layer"
        A1[主处理脚本 de_source_converter.py]
        A2[配置管理 config.py]
        A3[工具函数 utils.py]
    end
    
    subgraph "业务逻辑层 Business Logic Layer"
        B1[格式检测 Format Detection]
        B2[数据转换 Data Transformation]
        B3[内容处理 Content Processing]
        B4[质量控制 Quality Control]
    end
    
    subgraph "数据访问层 Data Access Layer"
        C1[Excel读取 pandas + openpyxl]
        C2[CSV写入 pandas.to_csv]
        C3[文件操作 pathlib]
    end
    
    subgraph "基础设施层 Infrastructure Layer"
        D1[Python 3.8+]
        D2[pandas 数据处理]
        D3[BeautifulSoup HTML解析]
        D4[logging 日志系统]
        D5[re 正则表达式]
    end
    
    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4
    
    B1 --> C1
    B2 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D2
    C2 --> D2
    C3 --> D1
    B3 --> D3
    A1 --> D4
    B1 --> D5
```

## 📈 性能监控架构

```mermaid
graph TD
    subgraph "性能指标 Performance Metrics"
        M1[处理速度 rows/sec]
        M2[内存使用 Memory Usage]
        M3[CPU使用率 CPU Usage]
        M4[磁盘I/O Disk I/O]
        M5[错误率 Error Rate]
    end
    
    subgraph "监控组件 Monitoring Components"
        C1[进度跟踪器 Progress Tracker]
        C2[资源监控器 Resource Monitor]
        C3[错误收集器 Error Collector]
        C4[性能分析器 Performance Analyzer]
    end
    
    subgraph "报告输出 Reporting Output"
        R1[实时控制台 Real-time Console]
        R2[日志文件 Log Files]
        R3[处理报告 Processing Report]
        R4[性能报告 Performance Report]
    end
    
    M1 --> C1
    M2 --> C2
    M3 --> C2
    M4 --> C2
    M5 --> C3
    
    C1 --> R1
    C2 --> R2
    C3 --> R2
    C4 --> R3
    C4 --> R4
```

---

**架构文档版本**: v1.0  
**创建日期**: 2025-09-01  
**技术负责人**: AI Assistant  
**状态**: 已实现并运行中
