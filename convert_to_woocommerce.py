#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将源数据文件转换为WooCommerce CSV导入格式
"""

import pandas as pd
import os
import re
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# WooCommerce CSV列映射
WC_COLUMNS = [
    'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
    'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
    'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
    'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
    'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
    'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
    'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
    'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
    'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
    'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
    'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
    'Brands', 'Meta: rank_math_focus_keyword'
]

def clean_text(text):
    """清理文本内容"""
    if pd.isna(text) or text == '':
        return ''
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', str(text))
    # 清理特殊字符
    text = re.sub(r'[^\w\s\-\.\,\|\&\#\(\)]', '', str(text))
    return str(text).strip()

def process_images(images_str, featured_str):
    """处理图片字段，如果没有图片则使用featured字段"""
    if pd.isna(images_str) or images_str == '':
        if pd.isna(featured_str) or featured_str == '':
            return ''
        return str(featured_str)
    
    # 如果包含分隔符，取第一个图片
    if '|||' in str(images_str):
        return str(images_str).split('|||')[0]
    
    return str(images_str)

def process_categories(categories_str):
    """处理分类字段，将分隔符转换为WooCommerce格式"""
    if pd.isna(categories_str) or categories_str == '':
        return ''
    
    # 将 ||| 分隔符转换为 > 分隔符
    categories = str(categories_str).replace('|||', ' > ')
    return categories

def process_tags(tags_str):
    """处理标签字段"""
    if pd.isna(tags_str) or tags_str == '':
        return ''
    
    # 将 ||| 分隔符转换为逗号分隔
    tags = str(tags_str).replace('|||', ', ')
    return tags

def convert_row_to_wc(row):
    """将源数据行转换为WooCommerce格式"""
    wc_row = {}
    
    # 基础字段映射
    wc_row['ID'] = ''  # 留空，让WooCommerce自动生成
    wc_row['Type'] = 'simple'
    wc_row['SKU'] = clean_text(row.get('sku', ''))
    wc_row['Name'] = clean_text(row.get('title', row.get('post_title', '')))
    wc_row['Published'] = 1
    wc_row['Is featured?'] = 0
    wc_row['Visibility in catalog'] = 'visible'
    
    # 描述字段
    wc_row['Short description'] = clean_text(row.get('post_excerpt', ''))
    wc_row['Description'] = clean_text(row.get('description', row.get('post_content', '')))
    
    # 价格字段
    wc_row['Sale price'] = ''
    wc_row['Regular price'] = clean_text(row.get('price', row.get('regular_price', '')))
    
    # 库存字段
    wc_row['In stock?'] = 1 if str(row.get('stock_status', '')).lower() == 'instock' else 0
    wc_row['Stock'] = clean_text(row.get('stock', ''))
    wc_row['Low stock amount'] = ''
    wc_row['Backorders allowed?'] = 0
    wc_row['Sold individually?'] = 0
    
    # 尺寸重量字段
    wc_row['Weight (kg)'] = clean_text(row.get('weight', ''))
    wc_row['Length (cm)'] = clean_text(row.get('length', ''))
    wc_row['Width (cm)'] = clean_text(row.get('width', ''))
    wc_row['Height (cm)'] = clean_text(row.get('height', ''))
    
    # 其他字段
    wc_row['Allow customer reviews?'] = 1
    wc_row['Purchase note'] = ''
    wc_row['Categories'] = process_categories(row.get('category', row.get('tax:product_cat', '')))
    wc_row['Tags'] = process_tags(row.get('tax:product_tag', ''))
    wc_row['Shipping class'] = ''
    
    # 图片处理
    wc_row['Images'] = process_images(
        row.get('images', row.get('image', '')),
        row.get('featured', '')
    )
    
    # 属性字段
    wc_row['Attribute 1 name'] = 'Brand'
    wc_row['Attribute 1 value(s)'] = clean_text(row.get('Brand', row.get('attribute:Brand', '')))
    wc_row['Attribute 1 visible'] = 1
    wc_row['Attribute 1 global'] = 0
    
    wc_row['Attribute 2 name'] = 'MPN'
    wc_row['Attribute 2 value(s)'] = clean_text(row.get('attribute:MPN', ''))
    wc_row['Attribute 2 visible'] = 1
    wc_row['Attribute 2 global'] = 0
    
    wc_row['Attribute 3 name'] = 'UPC'
    wc_row['Attribute 3 value(s)'] = clean_text(row.get('UPC', row.get('attribute:UPC', '')))
    wc_row['Attribute 3 visible'] = 1
    wc_row['Attribute 3 global'] = 0
    
    # 其他必需字段
    wc_row['Download limit'] = ''
    wc_row['Download expiry days'] = ''
    wc_row['Parent'] = ''
    wc_row['Grouped products'] = ''
    wc_row['Upsells'] = ''
    wc_row['Cross-sells'] = ''
    wc_row['External URL'] = ''
    wc_row['Button text'] = ''
    wc_row['Position'] = ''
    wc_row['Attribute 4 name'] = ''
    wc_row['Attribute 4 value(s)'] = ''
    wc_row['Attribute 4 visible'] = ''
    wc_row['Attribute 4 global'] = ''
    wc_row['Brands'] = clean_text(row.get('Brand', row.get('attribute:Brand', '')))
    wc_row['Meta: rank_math_focus_keyword'] = ''
    
    return wc_row

def convert_excel_to_wc(input_file, output_file):
    """将Excel文件转换为WooCommerce CSV格式"""
    try:
        logger.info(f"开始转换文件: {input_file}")
        
        # 读取Excel文件
        df = pd.read_excel(input_file)
        logger.info(f"读取到 {len(df)} 行数据")
        
        # 转换每一行
        wc_rows = []
        for index, row in df.iterrows():
            if index % 100 == 0:
                logger.info(f"已处理 {index} 行...")
            
            wc_row = convert_row_to_wc(row)
            wc_rows.append(wc_row)
        
        # 创建DataFrame
        wc_df = pd.DataFrame(wc_rows, columns=WC_COLUMNS)
        
        # 保存为CSV
        wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"转换完成，保存到: {output_file}")
        logger.info(f"总共转换 {len(wc_rows)} 行数据")
        
        return True
        
    except Exception as e:
        logger.error(f"转换失败: {e}")
        return False

def main():
    """主函数"""
    source_dir = Path("源数据文件")
    output_dir = Path("woocommerce_output")
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    if not source_dir.exists():
        logger.error("源数据文件目录不存在!")
        return
    
    # 获取所有Excel文件
    excel_files = list(source_dir.glob("*.xlsx"))
    
    if not excel_files:
        logger.error("没有找到Excel文件!")
        return
    
    logger.info(f"找到 {len(excel_files)} 个Excel文件:")
    for file in excel_files:
        logger.info(f"  - {file.name}")
    
    # 转换每个文件
    success_count = 0
    for file in excel_files:
        output_file = output_dir / f"{file.stem}_woocommerce.csv"
        
        logger.info(f"\n开始转换: {file.name}")
        if convert_excel_to_wc(file, output_file):
            success_count += 1
            logger.info(f"✓ {file.name} 转换成功")
        else:
            logger.error(f"✗ {file.name} 转换失败")
    
    logger.info(f"\n转换完成! 成功: {success_count}/{len(excel_files)}")
    logger.info(f"输出文件保存在: {output_dir.absolute()}")

if __name__ == "__main__":
    main() 