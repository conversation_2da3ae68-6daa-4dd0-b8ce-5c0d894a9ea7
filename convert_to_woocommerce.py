#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WooCommerce转换脚本 - 简化版本，专注于核心转换功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import sys

class WooCommerceConverter:
    def __init__(self):
        # WooCommerce必需字段
        self.required_fields = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 
            'Visibility in catalog', 'Short description', 'Description',
            'Tax status', 'In stock?', 'Stock', 'Regular price', 'Categories'
        ]
        
        # 完整的WooCommerce字段
        self.all_fields = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position'
        ]

    def clean_text(self, text):
        """清理文本内容"""
        if pd.isna(text) or text == '':
            return ''
        
        text = str(text).strip()
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 清理HTML标签（保留基本格式）
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<style[^>]*>.*?</style>', '', text, flags=re.DOTALL | re.IGNORECASE)
        
        return text

    def generate_sku(self, name, index):
        """生成SKU"""
        if pd.isna(name) or name == '':
            return f"PROD-{index:04d}"
        
        # 提取产品名称的关键词
        clean_name = re.sub(r'[^\w\s]', '', str(name))
        words = clean_name.split()
        
        if words:
            # 使用前两个单词的前3个字符
            sku_parts = []
            for word in words[:2]:
                if len(word) >= 3:
                    sku_parts.append(word[:3].upper())
            
            if sku_parts:
                return f"{''.join(sku_parts)}-{index:04d}"
        
        return f"PROD-{index:04d}"

    def detect_price_column(self, df):
        """检测价格列"""
        price_keywords = ['price', 'cost', 'amount', 'value', 'regular', 'list']
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in price_keywords):
                # 检查是否包含数值数据
                sample_data = df[col].dropna().head(10)
                if len(sample_data) > 0:
                    numeric_count = 0
                    for val in sample_data:
                        try:
                            float(str(val).replace('$', '').replace(',', ''))
                            numeric_count += 1
                        except:
                            continue
                    
                    if numeric_count / len(sample_data) > 0.5:  # 超过50%是数值
                        return col
        
        return None

    def detect_name_column(self, df):
        """检测产品名称列"""
        name_keywords = ['name', 'title', 'product', 'item', 'description']
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in name_keywords):
                # 检查数据质量
                sample_data = df[col].dropna().head(10)
                if len(sample_data) > 0:
                    avg_length = sum(len(str(val)) for val in sample_data) / len(sample_data)
                    if avg_length > 10:  # 平均长度大于10个字符
                        return col
        
        # 如果没找到，使用第一个文本列
        for col in df.columns:
            if df[col].dtype == 'object':
                return col
        
        return df.columns[0] if len(df.columns) > 0 else None

    def convert_file(self, input_file, output_file=None, minimal=False):
        """转换单个文件"""
        try:
            print(f"📁 读取文件: {Path(input_file).name}")
            
            # 读取数据
            if str(input_file).endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)
            
            print(f"📊 原始数据: {len(df)} 行, {len(df.columns)} 列")
            
            if len(df) == 0:
                print("❌ 文件为空")
                return None
            
            # 检测关键列
            name_col = self.detect_name_column(df)
            price_col = self.detect_price_column(df)
            
            print(f"🔍 检测结果:")
            print(f"  产品名称列: {name_col}")
            print(f"  价格列: {price_col}")
            
            # 创建WooCommerce数据
            fields_to_use = self.required_fields if minimal else self.all_fields
            wc_data = []
            
            for i, row in df.iterrows():
                wc_row = {}
                
                # 基本信息
                wc_row['ID'] = i + 1
                wc_row['Type'] = 'simple'
                
                # 产品名称
                if name_col:
                    product_name = self.clean_text(row[name_col])
                    wc_row['Name'] = product_name if product_name else f"Product {i+1}"
                else:
                    wc_row['Name'] = f"Product {i+1}"
                
                # SKU
                wc_row['SKU'] = self.generate_sku(wc_row['Name'], i+1)
                
                # 价格
                if price_col:
                    try:
                        price_str = str(row[price_col]).replace('$', '').replace(',', '').strip()
                        price = float(price_str) if price_str and price_str != 'nan' else 0
                        wc_row['Regular price'] = price if price > 0 else ''
                    except:
                        wc_row['Regular price'] = ''
                else:
                    wc_row['Regular price'] = ''
                
                # 描述（使用产品名称作为简短描述）
                wc_row['Short description'] = wc_row['Name']
                wc_row['Description'] = f"<p>{wc_row['Name']}</p>"
                
                # 分类（简单分类）
                wc_row['Categories'] = 'General'
                
                # 默认设置
                wc_row['Published'] = 1
                wc_row['Is featured?'] = 0
                wc_row['Visibility in catalog'] = 'visible'
                wc_row['Tax status'] = 'taxable'
                wc_row['In stock?'] = 1
                wc_row['Stock'] = 100
                
                # 如果不是最小模式，添加更多字段
                if not minimal:
                    wc_row['Tax class'] = ''
                    wc_row['Low stock amount'] = ''
                    wc_row['Backorders allowed?'] = 0
                    wc_row['Sold individually?'] = 0
                    wc_row['Weight (kg)'] = ''
                    wc_row['Length (cm)'] = ''
                    wc_row['Width (cm)'] = ''
                    wc_row['Height (cm)'] = ''
                    wc_row['Allow customer reviews?'] = 1
                    wc_row['Purchase note'] = ''
                    wc_row['Sale price'] = ''
                    wc_row['Tags'] = ''
                    wc_row['Shipping class'] = ''
                    wc_row['Images'] = ''
                    wc_row['Date sale price starts'] = ''
                    wc_row['Date sale price ends'] = ''
                    wc_row['Download limit'] = ''
                    wc_row['Download expiry days'] = ''
                    wc_row['Parent'] = ''
                    wc_row['Grouped products'] = ''
                    wc_row['Upsells'] = ''
                    wc_row['Cross-sells'] = ''
                    wc_row['External URL'] = ''
                    wc_row['Button text'] = ''
                    wc_row['Position'] = 0
                
                wc_data.append(wc_row)
            
            # 创建DataFrame
            wc_df = pd.DataFrame(wc_data)
            
            # 确保列顺序正确
            final_columns = [col for col in fields_to_use if col in wc_df.columns]
            wc_df = wc_df[final_columns]
            
            # 保存文件
            if output_file is None:
                input_path = Path(input_file)
                suffix = "_minimal" if minimal else "_full"
                output_file = f"woocommerce_{input_path.stem}{suffix}.csv"
            
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 转换完成!")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 产品数量: {len(wc_df)}")
            print(f"📋 字段数量: {len(wc_df.columns)}")
            
            return output_file
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def batch_convert(self, input_dir, output_dir=None, minimal=False):
        """批量转换"""
        input_path = Path(input_dir)
        
        if not input_path.exists():
            print(f"❌ 输入目录不存在: {input_dir}")
            return []
        
        if output_dir is None:
            output_dir = "woocommerce_converted"
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 查找文件
        files = list(input_path.glob("*.csv")) + list(input_path.glob("*.xlsx"))
        
        if not files:
            print(f"❌ 目录中没有数据文件")
            return []
        
        print(f"🚀 批量转换开始")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        print(f"文件数量: {len(files)}")
        print(f"模式: {'最小字段' if minimal else '完整字段'}")
        
        converted_files = []
        
        for i, file_path in enumerate(files, 1):
            print(f"\n[{i}/{len(files)}] " + "="*40)
            
            suffix = "_minimal" if minimal else "_full"
            output_file = output_path / f"woocommerce_{file_path.stem}{suffix}.csv"
            
            result = self.convert_file(str(file_path), str(output_file), minimal)
            
            if result:
                converted_files.append(result)
        
        print(f"\n🎉 批量转换完成!")
        print(f"成功转换: {len(converted_files)} 个文件")
        
        return converted_files

def main():
    """主函数"""
    converter = WooCommerceConverter()
    
    print("🔄 WooCommerce转换工具")
    print("="*40)
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        minimal = '--minimal' in sys.argv
        
        if Path(input_file).is_file():
            converter.convert_file(input_file, minimal=minimal)
        elif Path(input_file).is_dir():
            converter.batch_convert(input_file, minimal=minimal)
        else:
            print(f"❌ 路径不存在: {input_file}")
    else:
        print("📋 使用方法:")
        print("1. 转换单个文件:")
        print("   python convert_to_woocommerce.py input.csv")
        print("2. 批量转换目录:")
        print("   python convert_to_woocommerce.py input_directory/")
        print("3. 最小字段模式:")
        print("   python convert_to_woocommerce.py input.csv --minimal")
        
        # 检查input目录
        if Path("input").exists():
            print(f"\n💡 发现input目录，可以运行:")
            print(f"   python convert_to_woocommerce.py input/")

if __name__ == "__main__":
    main()
