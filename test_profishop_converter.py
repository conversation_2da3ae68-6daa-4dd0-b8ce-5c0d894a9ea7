#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Profishop-DE转换器
"""

import pandas as pd
from pathlib import Path
from profishop_de_converter import ProfishopDEConverter

def test_converter():
    """测试转换器功能"""
    print("🧪 测试Profishop-DE转换器")
    print("="*50)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'ID': [1, 2, 3],
        'title': ['Professional Drill Set', 'LED Work Light', 'Safety Helmet'],
        'description s': ['High quality drill', 'Bright LED light', 'Protective helmet'],
        'description': ['Basic description', 'Basic description', 'Basic description'],
        'price': [25.99, 8.50, 45.00],  # 包含小于10的价格
        'sku': ['DRILL001', 'LIGHT002', 'HELMET003'],
        'MFG': ['Bosch', 'Philips', 'SafetyFirst'],
        'Brand': ['<PERSON><PERSON>', 'Philips', 'SafetyFirst'],
        'UPC': ['123456789', '987654321', '456789123'],
        'image': ['http://example.com/drill.jpg', '', 'http://example.com/helmet.jpg'],  # 中间缺失
        'category': ['Tools > Power Tools', 'Lighting > Work Lights', 'Safety > Helmets'],
        'S-IMANGE': ['http://example.com/drill_s.jpg', 'http://example.com/light_s.jpg', 'http://example.com/helmet_s.jpg'],
        'tags': ['drill, power, tool', 'led, light, work', 'helmet, safety, protection'],
        'alldes': ['<h1>Professional Drill Set</h1><p>High quality professional drill set with multiple bits.</p>', 
                   '<h2>LED Work Light</h2><p>Bright LED work light for construction sites.</p>',
                   '<center>Safety Helmet</center><p>Protective helmet for construction workers.</p>'],
        'PageUrl': ['http://profishop.de/drill', 'http://profishop.de/light', 'http://profishop.de/helmet']
    })
    
    # 创建转换器
    converter = ProfishopDEConverter()
    
    print("📊 测试数据:")
    print(f"  行数: {len(test_data)}")
    print(f"  列数: {len(test_data.columns)}")
    
    # 测试字段映射
    print("\n🔍 测试字段映射:")
    for i, row in test_data.iterrows():
        print(f"\n产品 {i+1}:")
        print(f"  ID: {converter.get_field_value(row, 'id')}")
        print(f"  名称: {converter.get_field_value(row, 'name')}")
        print(f"  价格: {converter.get_field_value(row, 'price')}")
        print(f"  主图片: {converter.get_field_value(row, 'image_primary')}")
        print(f"  备用图片: {converter.get_field_value(row, 'image_secondary')}")
        print(f"  主要描述: {converter.get_field_value(row, 'description')[:50]}...")
    
    # 测试SKU生成
    print("\n🏷️  测试SKU生成:")
    for i, row in test_data.iterrows():
        sku = converter.generate_profishop_sku(row)
        print(f"  产品 {i+1}: {sku}")
    
    # 测试价格计算
    print("\n💰 测试价格计算:")
    for i, row in test_data.iterrows():
        price_str = converter.get_field_value(row, 'price')
        regular_price = float(price_str) if price_str else 0.0
        sale_price = converter.calculate_sale_price(regular_price)
        
        print(f"  产品 {i+1}: {regular_price} → {sale_price}")
        if regular_price < 10:
            print(f"    ✅ 小于10，无折扣")
        else:
            discount = (regular_price - sale_price) / regular_price * 100
            print(f"    💸 折扣: {discount:.1f}%")
    
    # 测试图片处理
    print("\n🖼️  测试图片处理:")
    for i, row in test_data.iterrows():
        image_result = converter.process_images(row)
        primary = converter.get_field_value(row, 'image_primary')
        secondary = converter.get_field_value(row, 'image_secondary')
        
        print(f"  产品 {i+1}:")
        print(f"    主图片: {primary if primary else '(空)'}")
        print(f"    备用图片: {secondary if secondary else '(空)'}")
        print(f"    最终结果: {image_result if image_result else '(空)'}")
        
        if not primary and secondary:
            print(f"    ✅ 使用备用图片替补")
    
    # 测试HTML清理
    print("\n🧹 测试HTML清理:")
    for i, row in test_data.iterrows():
        original = converter.get_field_value(row, 'description')
        cleaned = converter.clean_html_content(original)
        
        print(f"  产品 {i+1}:")
        print(f"    原始: {original}")
        print(f"    清理后: {cleaned}")
    
    # 测试完整转换
    print("\n🔄 测试完整转换:")
    wc_rows = []
    for i, row in test_data.iterrows():
        wc_row = converter.convert_row_to_wc(row)
        wc_rows.append(wc_row)
        
        print(f"  产品 {i+1}:")
        print(f"    SKU: {wc_row['SKU']}")
        print(f"    名称: {wc_row['Name']}")
        print(f"    常规价: {wc_row['Regular price']}")
        print(f"    促销价: {wc_row['Sale price']}")
        print(f"    图片: {wc_row['Images'][:50]}..." if wc_row['Images'] else "    图片: (空)")
    
    # 创建测试输出
    wc_df = pd.DataFrame(wc_rows, columns=converter.wc_columns)
    
    # 保存测试结果
    test_output_dir = Path("test_output")
    test_output_dir.mkdir(exist_ok=True)
    test_output_file = test_output_dir / "profishop_test_result.csv"
    
    wc_df.to_csv(test_output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 测试完成!")
    print(f"📁 测试结果保存到: {test_output_file}")
    print(f"📊 转换了 {len(wc_df)} 个产品")
    
    # 验证关键功能
    print(f"\n🎯 功能验证:")
    
    # 验证价格小于10的不打折
    low_price_products = wc_df[pd.to_numeric(wc_df['Regular price'], errors='coerce') < 10]
    if len(low_price_products) > 0:
        no_discount = (low_price_products['Regular price'] == low_price_products['Sale price']).all()
        print(f"  ✅ 小于10的价格不打折: {'通过' if no_discount else '失败'}")
    
    # 验证图片替补
    image_substitution = any('light_s.jpg' in img for img in wc_df['Images'] if img)
    print(f"  ✅ 图片替补功能: {'通过' if image_substitution else '失败'}")
    
    # 验证HTML清理
    html_cleaned = all('<h3>' in desc for desc in wc_df['Description'] if desc and '<h' in desc)
    print(f"  ✅ HTML标签清理: {'通过' if html_cleaned else '失败'}")
    
    # 验证SKU生成
    unique_skus = wc_df['SKU'].nunique() == len(wc_df)
    print(f"  ✅ SKU唯一性: {'通过' if unique_skus else '失败'}")

if __name__ == "__main__":
    test_converter()
